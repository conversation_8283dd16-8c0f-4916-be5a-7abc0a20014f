#ifndef ZGSTATISTIC_H
#define ZGSTATISTIC_H

#include <QObject>
#include <regex>
#include "ZGServerCommon.h"

class ZGStatistic : public QObject
{
    Q_OBJECT
public:
    enum class DataType
    {
        dtText, dtNumber, dtReal
    };
    struct StatisticParam
    {
        std::string id;
        std::string statisticTypeID;
        std::string statisticIntervalID;
        std::string statisticModeID;
        std::string tableName;
        std::string dataID;
        std::string fieldName;
        std::string key;
        DataType dataType{DataType::dtText};
        std::string dataValue;
        std::string sampleRatio;
        std::string operatorID;
        std::string operatorValue;
        std::string rtValue;
        std::string rtOccurTime;
        std::string rtSampleTotalValue;
        std::string rtSampleNum;
        std::string rtUpdateTime;
        std::string rtNewUpdateTime;
    };
public:
    explicit ZGStatistic(QObject *parent = nullptr);
    virtual bool initialize();
    void statistic(const std::string& id, bool start = false);

protected:
    virtual void calculateMax(const StatisticParam& statisticParam) = 0;
    virtual void calculateMin(const StatisticParam& statisticParam) = 0;
    virtual void calculateAvg(const StatisticParam& statisticParam) = 0;
    virtual void calculateCount(const StatisticParam& statisticParam) = 0;
    virtual void calculateSum(const StatisticParam& statisticParam) = 0;
    virtual bool parseStatisticParam(ZG6000::StringMap& mapFieldValue, StatisticParam& statisticParam);
    inline std::string dataTypeToString(DataType dataType);
    void updateStartStasticValue(const StatisticParam& statisticParam, bool start);

private:
    void initStatisticType();
    bool evaluateByMode(const StatisticParam& statisticParam, bool& result);

private:
    std::unordered_map<std::string, std::function<void(const StatisticParam&)>> m_mapTypeProcess;
};

#endif // ZGSTATISTIC_H

#include "ZGServerApplication.h"
#include "ZGMPHisStatisticProcessI.h"

const QString ZG_SERVER_VERSION = "V1.00.00.0000";
const QString ZG_APP_VERSION = ZG_SERVER_VERSION + ZGPubFun::getCompileVersion(__DATE__, __TIME__);

int main(int argc, char *argv[])
{
    ZGServerApplication a(argc, argv, ZG_APP_VERSION);
    ZG6000::ZGMPHisStatisticProcessI *pServer = new ZG6000::ZGMPHisStatisticProcessI;
    if(!a.startServer(pServer))
    {
        return -1;
    }

    return a.exec();
}

#include "ZGMPHistStatistic.h"
#include "ZGProxyCommon.h"
#include "ZGDebugMng.h"
#include "ZGUtils.h"
#include <sstream>

const std::string DateTimeFormat = "yyyy-MM-dd hh:mm:ss.zzz";

ZGMPHistStatistic::ZGMPHistStatistic(QObject* parent) : ZGStatistic(parent)
{
}

bool ZGMPHistStatistic::initialize()
{
    if (!ZGStatistic::initialize())
        return false;
    if (!initStoreParam())
        return false;
    if (!initChangeParam())
        return false;
    return true;
}

void ZGMPHistStatistic::calculateMax(const StatisticParam& statisticParam)
{
    calculateByStore(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)-> void
        {
            try
            {
                const auto & rtNewValue = ZGUtils::get(listResult[0], "rtNewValue");
                const auto & rtStoreTime = ZGUtils::get(listResult[0], "rtStoreTime");
                double newValue = std::stod(rtNewValue);
                if (statisticParam.rtValue.empty())
                {
                    mapFieldValue.insert(std::make_pair("rtValue", std::to_string(newValue)));
                    mapFieldValue.insert(std::make_pair("rtOccurTime", rtStoreTime));
                }
                else
                {
                    double oldValue = std::atof(statisticParam.rtValue.c_str());
                    if (newValue - oldValue > ZGUtils::eps)
                    {
                        mapFieldValue.insert(std::make_pair("rtValue", rtNewValue));
                        mapFieldValue.insert(std::make_pair("rtOccurTime", rtStoreTime));
                    }
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(QString("calculate error, %1, statisticID = %2")
                    .arg(e.what()).arg(statisticParam.id.c_str()));
            }
        });
}

void ZGMPHistStatistic::calculateMin(const StatisticParam& statisticParam)
{
    calculateByStore(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)-> void
        {
            try
            {
                const auto & rtNewValue = ZGUtils::get(listResult[listResult.size() - 1], "rtNewValue");
                const auto & rtStoreTime = ZGUtils::get(listResult[listResult.size() - 1], "rtStoreTime");
                double newValue = std::stod(rtNewValue);
                if (statisticParam.rtValue.empty())
                {
                    mapFieldValue.insert(std::make_pair("rtValue", std::to_string(newValue)));
                    mapFieldValue.insert(std::make_pair("rtOccurTime", rtStoreTime));
                }
                else
                {
                    double oldValue = std::atof(statisticParam.rtValue.c_str());
                    if (oldValue - newValue > ZGUtils::eps)
                    {
                        mapFieldValue.insert(std::make_pair("rtValue", rtNewValue));
                        mapFieldValue.insert(std::make_pair("rtOccurTime", rtStoreTime));
                    }
                }
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(QString("calculate error, %1, statisticID = %2")
                    .arg(e.what()).arg(statisticParam.id.c_str()));
            }
        });
}

void ZGMPHistStatistic::calculateAvg(const StatisticParam& statisticParam)
{
    calculateByStore(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)-> void
        {
            try
            {
                double totalValue = std::atof(statisticParam.rtSampleTotalValue.c_str());
                int totalNum = std::atoi(statisticParam.rtSampleNum.c_str());
                for (const auto& result : listResult)
                {
                    try
                    {
                        double value = std::stod(ZGUtils::get(result, "rtNewValue"));
                        totalValue += value;
                        ++totalNum;
                    }
                    catch (const std::exception& e)
                    {
                        ZGLOG_WARN(e.what());
                    }
                }
                double newAvgValue = totalValue / totalNum;
                mapFieldValue.insert(std::make_pair("rtSampleTotalValue", std::to_string(totalValue)));
                mapFieldValue.insert(std::make_pair("rtSampleNum", std::to_string(totalNum)));
                mapFieldValue.insert(std::make_pair("rtValue", std::to_string(newAvgValue)));
                mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(QString("calculate error, %1, statisticID = %2")
                    .arg(e.what()).arg(statisticParam.id.c_str()));
            }
        });
}

void ZGMPHistStatistic::calculateCount(const StatisticParam& statisticParam)
{
    calculateByChange(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)->void
        {
            int count = std::atoi(statisticParam.rtValue.c_str());
            count += static_cast<int>(listResult.size());
            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(count)));
            mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        });
}

void ZGMPHistStatistic::calculateSum(const StatisticParam& statisticParam)
{
    calculateByChange(statisticParam,
        [&statisticParam](const ZG6000::ListStringMap& listResult, ZG6000::StringMap& mapFieldValue)->void
        {
            double totalValue = std::atof(statisticParam.rtValue.c_str());
            for (const auto& result : listResult)
            {
                try
                {
                    const auto & newValue = ZGUtils::get(result, "newValue");
                    double value = std::stod(newValue);
                    totalValue += value;
                }
                catch (const std::exception& e)
                {
                    ZGLOG_WARN(e.what());
                }
            }
            mapFieldValue.insert(std::make_pair("rtValue", std::to_string(totalValue)));
            mapFieldValue.insert(std::make_pair("rtOccurTime", statisticParam.rtNewUpdateTime));
        });
}

bool ZGMPHistStatistic::initStoreParam()
{
    if (!initTableStoreParam("mp_param_dataset_yc", m_setStoreYc))
        return false;
    return true;
}

bool ZGMPHistStatistic::initChangeParam()
{
    if (!initTableChangeParam("mp_param_dataset_yc", "mp_param_model_yc", m_setChangeYc))
        return false;
    if (!initTableChangeParam("mp_param_dataset_yx", "mp_param_model_yx", m_setChangeYx))
        return false;
    if (!initTableChangeParam("mp_param_dataset_text", "mp_param_model_text", m_setChangeText))
        return false;
    return true;
}

bool ZGMPHistStatistic::initTableStoreParam(const std::string& paramTable, std::unordered_set<std::string>& changeSet)
{
    QString sql = QString("SELECT id FROM mp_param_dataset_store WHERE tableName = '%1'").arg(paramTable.c_str());
    ZG6000::StringList listResult;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listResult))
    {
        ZGLOG_ERROR(QString("init %1 store param error.").arg(paramTable.c_str()));
        return false;
    }
    for (const auto& result : listResult)
    {
        m_setStoreYc.insert(result);
        qDebug() << result.c_str();
    }
    return true;
}

bool ZGMPHistStatistic::initTableChangeParam(const std::string& paramTable, const std::string& modelTable,
    std::unordered_set<std::string>& changeSet)
{
    QString sql = QString("SELECT %1.id FROM %1 LEFT JOIN %2"
        " ON %1.dataModelID = %2.id WHERE %2.isChangeStore = '1'").arg(paramTable.c_str()).arg(modelTable.c_str());
    ZG6000::StringList listResult;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listResult))
    {
        ZGLOG_ERROR(QString("init %1 change param error.").arg(paramTable.c_str()));
        return false;
    }
    for (const auto& result : listResult)
    {
        changeSet.insert(result);
    }
    return true;
}

void ZGMPHistStatistic::calculateByStore(const StatisticParam& statisticParam, const Calculate& calculate)
{
    if (!isDataInStore(statisticParam.tableName, statisticParam.dataID))
        return;
    if (statisticParam.rtUpdateTime.empty())
        updateStartStasticValue(statisticParam, false);
    else
    {
        ZG6000::ListStringMap listResult;
        if (!getDatasetFromStore(statisticParam, listResult))
        {
            ZGLOG_ERROR("getDatasetFromStore error.");
            return;
        }
        ZG6000::StringMap mapFieldValue;
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        if (statisticParam.sampleRatio != "100")
        {
            ZG6000::ListStringMap listFilterResult;
            if (filterDataset(listResult, listFilterResult, statisticParam.sampleRatio, ""))
            {
                if (!listFilterResult.empty())
                    calculate(listFilterResult, mapFieldValue);
            }
        }
        else
        {
            if (!listResult.empty())
                calculate(listResult, mapFieldValue);
        }
        if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
        {
            ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
        }
    }
}

void ZGMPHistStatistic::calculateByChange(const StatisticParam& statisticParam, const ZGMPHistStatistic::Calculate& calculate)
{
    if (!isDataInChange(statisticParam.tableName, statisticParam.dataID))
        return;
    if (statisticParam.rtUpdateTime.empty())
        updateStartStasticValue(statisticParam, false);
    else
    {
        ZG6000::ListStringMap listResult;
        if (!getDatasetFromChange(statisticParam, listResult))
        {
            ZGLOG_ERROR("getDatasetFromChange error.");
            return;
        }
        ZG6000::StringMap mapFieldValue;
        mapFieldValue.insert(std::make_pair("rtUpdateTime", statisticParam.rtNewUpdateTime));
        if (!listResult.empty())
            calculate(listResult, mapFieldValue);
        if (!ZGProxyCommon::updateDataByID("sp_param_statistic", statisticParam.id, mapFieldValue))
            ZGLOG_ERROR(QString("updateDataByIDFromMap error, id = %1").arg(statisticParam.id.c_str()));
    }
}

bool ZGMPHistStatistic::isDataInStore(const std::string& tableName, const std::string& dataID)
{
    if (tableName == "mp_param_dataset_yc")
    {
        if (m_setStoreYc.find(dataID) == m_setStoreYc.end())
        {
            ZGLOG_ERROR(QString("Can't find yc id %1 in store param.").arg(dataID.c_str()));
            return false;
        }
        return true;
    }
    ZGLOG_ERROR(QString("Invalid store table %1.").arg(tableName.c_str()));
    return false;
}

bool ZGMPHistStatistic::isDataInChange(const std::string& tableName, const std::string& dataID)
{
    if (tableName == "mp_param_dataset_yc")
    {
        if (m_setChangeYc.find(dataID) == m_setChangeYc.end())
        {
            ZGLOG_ERROR("Can't find yc id %1 in change param.");
            return false;
        }
        return true;
    }
    if (tableName == "mp_param_dataset_yx")
    {
        if (m_setChangeYx.find(dataID) == m_setChangeYx.end())
        {
            ZGLOG_ERROR("Can't find yx id %1 in change param.");
            return false;
        }
        return true;
    }
    if (tableName == "mp_param_dataset_text")
    {
        if (m_setChangeText.find(dataID) == m_setChangeText.end())
        {
            ZGLOG_ERROR("Can't find text id %1 in change param.");
            return false;
        }
        return true;
    }
    ZGLOG_ERROR(QString("Invalid change table %1.").arg(tableName.c_str()));
    return false;
}

bool ZGMPHistStatistic::getStoreTableName(const std::string& realTableName, const std::string& updateTime,
    std::string& storeTableName)
{
    std::string updateDate;
    size_t blankPos = updateTime.find_first_of(" ");
    if (blankPos != std::string::npos)
        updateDate = updateTime.substr(0, blankPos);
    std::vector<std::string> listDate;
    int count = ZGUtils::splitString(updateDate, "-", listDate);
    if (count != 3)
    {
        ZGLOG_ERROR(QString("%1 is not a valid datetime format.").arg(updateTime.c_str()));
        return false;
    }
    ZG6000::StringList listString;
    count = ZGUtils::splitString(realTableName, "_", listString);
    if (count != 4)
    {
        ZGLOG_ERROR(QString("%1 is not a valid table.").arg(realTableName.c_str()));
        return false;
    }
    std::stringstream ss;
    ss << listString[0] << "_" << "his" << "_" << listString[2] << "_store_" << listString[3]
        << "_" << listDate[0] << listDate[1];
    storeTableName = ss.str();
    return true;
}

bool ZGMPHistStatistic::getChangeTableName(const std::string& realTableName, const std::string& updateTime, std::string& storeTableName)
{
    std::string updateDate;
    size_t blankPos = updateTime.find_first_of(" ");
    if (blankPos != std::string::npos)
        updateDate = updateTime.substr(0, blankPos);
    std::vector<std::string> listDate;
    int count = ZGUtils::splitString(updateDate, "-", listDate);
    if (count != 3)
    {
        ZGLOG_ERROR(QString("%1 is not a valid datetime format.").arg(updateTime.c_str()));
        return false;
    }
    storeTableName = realTableName;
    auto pos = storeTableName.find("param");
    storeTableName.replace(pos, std::strlen("param"), "his");
    storeTableName += "_" + listDate[0] + listDate[1];
    return true;
}

std::string ZGMPHistStatistic::generateQueryCondition(const StatisticParam& statisticParam)
{
    // TODO 根据操作符和比较值生成查询条件，可暂时不做
    return "";
}

bool ZGMPHistStatistic::getDatasetFromStore(const StatisticParam& statisticParam, ZG6000::ListStringMap& listResult)
{
    std::string storeTableName;
    if (!getStoreTableName(statisticParam.tableName, statisticParam.rtUpdateTime, storeTableName))
        return false;
    std::string fieldName = statisticParam.fieldName;
    if (statisticParam.statisticModeID == "ZG_SM_ABS")
        fieldName = "ABS(" + fieldName + ") AS " + fieldName;
    fieldName += ", rtStoreTime";
    std::string condition = "dataID = '" + statisticParam.dataID + "'";
    condition += " AND rtStoreTime > '" + statisticParam.rtUpdateTime + "' AND rtStoreTime < '" +
        statisticParam.rtNewUpdateTime + "'";
    if (statisticParam.statisticModeID == "ZG_SM_POS")
        condition += " AND " + statisticParam.fieldName + " >= 0";
    else if (statisticParam.statisticModeID == "ZG_SM_NEG")
        condition += " AND " + statisticParam.fieldName + " <= 0";
    if (!statisticParam.operatorID.empty() && !statisticParam.operatorValue.empty())
    {
        std::string operatorID = statisticParam.operatorID;
        if (operatorID == "==")
            operatorID = "=";
        condition += " AND " + statisticParam.fieldName + " " + operatorID + " '" +
            statisticParam.operatorValue + "'";
    }
    condition += " ORDER BY " + statisticParam.fieldName + " DESC";
    std::string sql = "SELECT " + fieldName + " FROM " + storeTableName + " WHERE " + condition;
    qDebug() << sql.c_str();
    return ZGProxyCommon::execQuerySql(sql, listResult, true);
}

bool ZGMPHistStatistic::getDatasetFromChange(const StatisticParam& statisticParam, ZG6000::ListStringMap& listResult)
{
    std::string changeTableName;
    if (!getChangeTableName(statisticParam.tableName, statisticParam.rtUpdateTime, changeTableName))
        return false;
    std::string fieldName = "newValue";
    if (statisticParam.statisticModeID == "ZG_SM_ABS")
        fieldName = "ABS(" + fieldName + ") AS newValue";
    fieldName += ", changeTime";
    std::string condition = "dataID = '" + statisticParam.dataID + "' AND fieldName = '" + statisticParam.fieldName + "'";
    condition +=  " AND changeTime > '" + statisticParam.rtUpdateTime + "' AND changeTime < '" + statisticParam
        .rtNewUpdateTime + "'";
    if (statisticParam.statisticModeID == "ZG_SM_POS")
        condition += " AND newValue >= '0'";
    else if (statisticParam.statisticModeID == "ZG_SM_NEG")
        condition += " AND newValue <= '0'";
    if (!statisticParam.operatorID.empty() && !statisticParam.operatorValue.empty())
    {
        std::string operatorID = statisticParam.operatorID;
        if (operatorID == "==")
            operatorID = "=";
        condition += " AND newValue " + operatorID + " '" +
            statisticParam.operatorValue + "'";
    }
    condition += " ORDER BY changeTime DESC";
    std::string sql = "SELECT " + fieldName + " FROM " + changeTableName + " WHERE " + condition;
    qDebug() << sql.c_str();
    return ZGProxyCommon::execQuerySql(sql, listResult, true);
}

bool ZGMPHistStatistic::filterDataset(ZG6000::ListStringMap& srcListResult, ZG6000::ListStringMap& destListResult, const std::string& sampleRatio, const std::string& sampleRatioType)
{
    try
    {
        double ratio = std::stod(sampleRatio) / 100;
        int filterNum = static_cast<int>(ratio * static_cast<double>(srcListResult.size()));
        int moveNum = static_cast<int>(srcListResult.size()) - filterNum;
        if (filterNum > 0 && moveNum > 0)
        {
            std::move(srcListResult.begin() + moveNum, srcListResult.end(), std::back_inserter(destListResult));
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

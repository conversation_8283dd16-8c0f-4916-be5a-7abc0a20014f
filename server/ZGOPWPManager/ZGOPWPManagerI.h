#ifndef ZG6000_ZGOPWPMANAGERI_H
#define ZG6000_ZGOPWPMANAGERI_H

#include <ZGOPWPManager.h>

namespace ZG6000 {

class ZGOPWPManagerI : public ZG6000::ZGOPWPManager
{
public:
    ZGOPWPManagerI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current) override;

    // ZGOPWPManager interface
public:
    bool getWPTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current) override;
    bool createWPTask(StringMap param, StringList listUserID, std::string &taskID, ErrorInfo &e, const Ice::Current &current) override;
    bool editWPTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool editWPUser(std::string taskID, StringList listUserID, ErrorInfo& e, const Ice::Current& current) override;
    bool deleteWPTask(std::string taskID, ErrorInfo& e, const Ice::Current& current) override;
    bool abolishWPTask(std::string taskID, ErrorInfo& e, const Ice::Current& current) override;
    bool confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current) override;
    bool backTask(std::string taskID, StringMap param, ErrorInfo& e, const Ice::Current& current) override;
    bool getWPUser(std::string taskID, ListStringMap &listUser, ErrorInfo &e, const Ice::Current &current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGOPWPMANAGERI_H

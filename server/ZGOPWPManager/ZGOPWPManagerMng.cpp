#include "ZGOPWPManagerMng.h"

#include <QJsonDocument>
#include <QJsonObject>
#include <QThread>

#include "ZGRuntime.h"
#include "ZGUtils.h"
#include "zgerror/ZGOPWPManagerError.h"

namespace ZG6000
{
    ZGOPWPManagerMng::ZGOPWPManagerMng(QObject* parent)
        : QObject{parent}
    {
    }

    void ZGOPWPManagerMng::initServerInstConfig()
    {
        const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
        QString errMsg;
        int value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_initInterval = value;
        if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
            ZGLOG_WARN(errMsg);
        else
            m_checkInterval = value;
    }

    bool ZGOPWPManagerMng::initServerInstInfo()
    {
        m_serverName = ZGRuntime::instance()->getServerID();
        if (m_serverName.isEmpty())
        {
            ZGLOG_ERROR("Empty server id.");
            return false;
        }
        m_instName = ZGRuntime::instance()->getInstanceID();
        if (m_instName.isEmpty())
        {
            ZGLOG_ERROR("Empty server instance id.");
            return false;
        }
        return true;
    }

    bool ZGOPWPManagerMng::initParams()
    {
        std::string sql = "SELECT id, name FROM op_dict_wp_stage";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapStage))
        {
            ZGLOG_ERROR(QStringLiteral("获取任务状态信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM sp_param_appnode";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapAppNode))
        {
            ZGLOG_ERROR(QStringLiteral("获取应用节点信息失败"));
            return false;
        }
        sql = "SELECT id, name FROM mp_param_region";
        if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapRegion))
        {
            ZGLOG_ERROR(QStringLiteral("获取区域信息失败"));
            return false;
        }
        sql = "SELECT examID FROM op_param_wp_system";
        StringList listExamID;
        if (!ZGProxyCommon::execQuerySqlCol(sql, listExamID))
        {
            ZGLOG_ERROR(QStringLiteral("获取请销点审批ID失败"));
            return false;
        }
        if (listExamID.empty())
        {
            ZGLOG_ERROR(QStringLiteral("未指定审批流程"));
            return false;
        }
        if (listExamID.size() > 1)
        {
            ZGLOG_ERROR(QStringLiteral("指定的审批流程不唯一"));
            return false;
        }
        m_examID = listExamID[0];
        return true;
    }

    bool ZGOPWPManagerMng::initMqttClient()
    {
        if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
        {
            ZGLOG_ERROR("initMqttClient error.");
            return false;
        }
        m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
        if (m_pMqttClient == nullptr)
        {
            ZGLOG_ERROR("getMqttClientMessage error.");
            return false;
        }
        m_pMqttClient->connectToHost();
        return true;
    }

    bool ZGOPWPManagerMng::onCreateConfirm(const std::string& taskID, ErrorInfo& e)
    {
        std::string requestWorkExamID;
        if (!ZGProxyCommon::getDataByField("op_param_wp", taskID, "requestWorkExamID", requestWorkExamID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取任务'%1'参数失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!requestWorkExamID.empty())
        {
            if (!examCall([&](std::shared_ptr<ZGSPExamManagerPrx> examProxy)
            {
                return examProxy->deleteExam(requestWorkExamID, e);
            }, e))
                return false;
        }
        std::string realExamID;
        if (!examCall([&](std::shared_ptr<ZGSPExamManagerPrx> examProxy)
        {
            return examProxy->createExam(m_examID, realExamID, e);
        }, e))
            return false;
        StringMap task{
            {"id", taskID}, {"stageID", "ZG_WS_REQUEST_EXAM"}, {"requestWorkExamID", realExamID},
            {"requestWorkTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()}
        };
        const auto& sql = ZGUtils::generateUpdateSql("op_param_wp", task);
        if (!ZGProxyCommon::execSql(sql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("任务'%1'创建提交失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }

    bool ZGOPWPManagerMng::onRequestExamConfirm(const std::string& taskID, const std::string& examID, ErrorInfo& e)
    {
        std::string examStateID;
        if (!ZGProxyCommon::getDataByField("sp_real_exam", examID, "examStateID", examStateID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取审批'%1'状态失败").arg(examID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (examStateID != "ZG_ES_ACCEPT")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("审批'%1'未完成").arg(examID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringMap task{
            {"id", taskID}, {"stageID", "ZG_WS_EXECUTE"}
        };
        const auto& sql = ZGUtils::generateUpdateSql("op_param_wp", task);
        if (!ZGProxyCommon::execSql(sql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("任务'%1'请点审批提交失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }

    bool ZGOPWPManagerMng::onExecuteConfirm(const std::string& taskID, ErrorInfo& e)
    {
        std::string finishWorkExamID;
        if (!ZGProxyCommon::getDataByField("op_param_wp", taskID, "finishWorkExamID", finishWorkExamID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取任务'%1'参数失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!finishWorkExamID.empty())
        {
            if (!examCall([&](std::shared_ptr<ZGSPExamManagerPrx> examProxy)
            {
                return examProxy->deleteExam(finishWorkExamID, e);
            }, e))
                return false;
        }
        std::string realExamID;
        if (!examCall([&](std::shared_ptr<ZGSPExamManagerPrx> examProxy)
        {
            return examProxy->createExam(m_examID, realExamID, e);
        }, e))
            return false;
        StringMap task{
            {"id", taskID}, {"stageID", "ZG_WS_FINISH_EXAM"}, {"finishWorkExamID", realExamID},
            {"finishWorkTime", ZGUtils::DateTimeToString(QDateTime::currentDateTime()).toStdString()}
        };
        const auto& sql = ZGUtils::generateUpdateSql("op_param_wp", task);
        if (!ZGProxyCommon::execSql(sql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("任务'%1'执行提交失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }

    bool ZGOPWPManagerMng::onFinishExamConfirm(const std::string& taskID, const std::string& examID, ErrorInfo& e)
    {
        std::string examStateID;
        if (!ZGProxyCommon::getDataByField("sp_real_exam", examID, "examStateID", examStateID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取审批'%1'状态失败").arg(examID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (examStateID != "ZG_ES_ACCEPT")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("审批'%1'未完成").arg(examID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!saveTask(taskID, e))
            return false;
        if (!removeTask(taskID, e))
            return false;
        return true;
    }

    bool ZGOPWPManagerMng::examCall(const std::function<bool(std::shared_ptr<ZGSPExamManagerPrx>)>& func, ErrorInfo& e)
    {
        auto examProxy = ZGProxyMng::instance()->getProxySPExamManager();
        if (examProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取审批管理服务代理对象失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            return func(examProxy);
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPWPManagerMng::finishExam(const std::string& examID, ErrorInfo& e)
    {
        if (!examCall([&](std::shared_ptr<ZGSPExamManagerPrx> examProxy)
        {
            return examProxy->finishExam(examID, e);
        }, e))
            return false;
        return true;
    }

    bool ZGOPWPManagerMng::saveTask(const std::string& taskID, ErrorInfo& e)
    {
        ZG6000::StringMap task;
        if (!ZGProxyCommon::getDataByID("op_param_wp", taskID, task))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取任务'%1'信息失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& requestWorkExamID = task["requestWorkExamID"];
        if (!requestWorkExamID.empty())
        {
            if (!finishExam(requestWorkExamID, e))
                return false;
        }
        const auto& finishWorkExamID = task["finishWorkExamID"];
        if (!finishWorkExamID.empty())
        {
            if (!finishExam(finishWorkExamID, e))
                return false;
        }
        QDateTime dt = QDateTime::currentDateTime();
        QString hisWpTable = QString("op_his_wp_%1").arg(dt.date().year());
        QString hisWpUserTable = QString("op_his_wp_user_%1").arg(dt.date().year());
        StringList listSql;
        listSql.push_back(QString("DELETE FROM %1 WHERE id = '%2';").arg(hisWpTable).arg(taskID.c_str()).toStdString());
        listSql.push_back(QString("DELETE FROM %1 WHERE workPointID = '%2';").arg(hisWpUserTable).arg(taskID.c_str()).toStdString());
        const auto& appNodeName = ZGUtils::get(m_mapAppNode, task["appNodeID"], "");
        task["appNodeName"] = appNodeName;
        const auto& regionName = ZGUtils::get(m_mapRegion, task["regionID"], "");
        task["regionName"] = regionName;
        const auto& stageName = ZGUtils::get(m_mapStage, task["stageID"], "");
        task["stageName"] = stageName;
        std::string workLeaderName;
        ZGProxyCommon::getDataByField("sp_param_hrm_user", task["workLeaderID"], "name", workLeaderName);
        task["workLeaderName"] = workLeaderName;
        listSql.push_back(ZGUtils::generateInsertSql(hisWpTable.toStdString(), task));
        QString sql = QString("SELECT * FROM op_param_wp_user WHERE workPointID = '%1'").arg(taskID.c_str());
        ListStringMap listUser;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUser))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务'%1'人员信息失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (auto& user : listUser)
        {
            std::string userName;
            if (!ZGProxyCommon::getDataByField("sp_param_hrm_user", user["userID"], "name", userName))
            {
                e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_RT);
                e.errDetail = QStringLiteral("获取人员'%1'名称失败").arg(user["userID"].c_str()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            user["userName"] = userName;
            listSql.push_back(ZGUtils::generateInsertSql(hisWpUserTable.toStdString(), user));
        }
        if (!ZGProxyCommon::execBatchSql(listSql, true))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("保存任务'%1'失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPWPManagerMng::removeTask(const std::string& taskID, ErrorInfo& e)
    {
        StringList listSql;
        listSql.push_back(QString("DELETE FROM op_param_wp WHERE id = '%1';").arg(taskID.c_str()).toStdString());
        listSql.push_back(QString("DELETE FROM op_param_wp_user WHERE workPointID = '%1';").arg(taskID.c_str()).toStdString());
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("删除任务'%1'失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }

    ZGOPWPManagerMng* ZGOPWPManagerMng::instance()
    {
        if (g_pInstance == nullptr)
            g_pInstance = new ZGOPWPManagerMng;
        return g_pInstance;
    }

    void ZGOPWPManagerMng::init()
    {
        initServerInstConfig();
        while (!initServerInstInfo())
        {
            ZGLOG_ERROR("initServerInstInfo error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initParams())
        {
            ZGLOG_ERROR("initParams error.");
            QThread::msleep(m_initInterval * 1000);
        }
        while (!initMqttClient())
        {
            ZGLOG_ERROR("initMqttClient error.");
            QThread::msleep(m_initInterval * 1000);
        }
        m_initialized = true;
        ZGLOG_INFO("ZGOPWPManager init finished.");
    }

    bool ZGOPWPManagerMng::checkState()
    {
        return m_initialized;
    }

    void ZGOPWPManagerMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord)
    {
        if (!m_initialized)
            return;
        if (reason != "change")
            return;
        try
        {
            for (const auto& record : listRecord)
            {
                QJsonObject object;
                for (const auto & [field, value] : record)
                {
                    object[field.c_str()] = value.newValue.c_str();
                    if (field == "appNodeID")
                        object["appNodeName"] = ZGUtils::get(m_mapAppNode, value.newValue, "").c_str();
                    if (field == "regionID")
                        object["regionName"] = ZGUtils::get(m_mapRegion, value.newValue, "").c_str();
                    if (field == "stageID")
                        object["stageName"] = ZGUtils::get(m_mapStage, value.newValue, "").c_str();
                    if (field == "workLeaderID")
                    {
                        std::string userName;
                        ZGProxyCommon::getDataByField("sp_param_hrm_user", value.newValue, "name", userName);
                        object["workLeaderName"] = userName.c_str();
                    }
                }
                QJsonDocument doc(object);
                m_pMqttClient->sendPublish(QString("op_param_wp/%1").arg(oper.c_str()), doc.toJson());
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }

    bool ZGOPWPManagerMng::getWPTaskList(StringMap param, ListStringMap& listTask, ErrorInfo& e)
    {
        std::string condition = param.find("condition") != param.end() ? param["condition"] : "1=1";
        std::string orderType = param.find("order") != param.end() ? param["order"] : "ASC";
        std::string orderField = param.find("sort") != param.end() ? param["sort"] : "id";
        std::string offset = param.find("offset") != param.end() ? param["offset"] : "0";
        std::string limit = param.find("limit") != param.end() ? param["limit"] : "1000";
        QString addition = QString(" ORDER BY %1 %2 LIMIT %3, %4").arg(orderField.c_str())
                                                                  .arg(orderType.c_str()).arg(offset.c_str()).arg(limit.c_str());
        QString sql = QString("SELECT * FROM op_param_wp WHERE %1").arg(condition.c_str()) + addition;
        auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
        if (dbProxy == nullptr)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("获取数据服务代理对象失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        try
        {
            if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listTask, e))
            {
                ZGLOG_ERROR(e);
                return false;
            }
            for (auto& task : listTask)
            {
                task["appNodeName"] = ZGUtils::get(m_mapAppNode, task["appNodeID"], "");
                task["stageName"] = ZGUtils::get(m_mapStage, task["stageID"], "");
                task["regionName"] = ZGUtils::get(m_mapRegion, task["regionID"], "");
                std::string workLeaderName;
                ZGProxyCommon::getDataByField("sp_param_hrm_user", task["workLeaderID"], "name", workLeaderName);
                std::string requestWorkExamStateID, finishWorkExamStateID;
                if (!task["requestWorkExamID"].empty())
                    ZGProxyCommon::getDataByField("sp_real_exam", task["requestWorkExamID"], "examStateID", requestWorkExamStateID);
                if (!task["finishWorkExamID"].empty())
                    ZGProxyCommon::getDataByField("sp_real_exam", task["finishWorkExamID"], "examStateID", finishWorkExamStateID);
                task["requestWorkExamStateID"] = requestWorkExamStateID;
                task["finishWorkExamStateID"] = finishWorkExamStateID;
                task["workLeaderName"] = workLeaderName;
            }
            return true;
        }
        catch (const Ice::Exception& ie)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = ie.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPWPManagerMng::createWPTask(StringMap param, StringList listUserID, std::string& taskID, ErrorInfo& e)
    {
        if (!ZGProxyCommon::createUUID(taskID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建任务ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZG6000::StringList listSql;
        param["id"] = taskID;
        param["stageID"] = "ZG_WS_CREATE";
        const auto& sql = ZGUtils::generateInsertSql("op_param_wp", param);
        listSql.push_back(sql);
        StringList listID;
        if (!ZGProxyCommon::createUUID(static_cast<int>(listUserID.size()), listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建请销点用户ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (size_t i = 0; i < listID.size(); ++i)
        {
            StringMap wpUser{{"id", listID[i]}, {"workPointID", taskID}, {"userID", listUserID[i]}};
            listSql.push_back(ZGUtils::generateInsertSql("op_param_wp_user", wpUser));
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建任务'%1'失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }

    bool ZGOPWPManagerMng::editWPTask(std::string taskID, StringMap param, ErrorInfo& e)
    {
        param["id"] = taskID;
        const auto& sql = ZGUtils::generateUpdateSql("op_param_wp", param);
        if (!ZGProxyCommon::execSql(sql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务'%1'失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZGProxyCommon::synchronize();
        return true;
    }

    bool ZGOPWPManagerMng::editWPUser(std::string taskID, StringList listUserID, ErrorInfo& e)
    {
        ZG6000::StringList listSql;
        QString sql = QString("DELETE FROM op_param_wp_user WHERE workPointID = '%1';").arg(taskID.c_str());
        listSql.push_back(sql.toStdString());
        ZG6000::StringList listID;
        if (!ZGProxyCommon::createUUID(static_cast<int>(listUserID.size()), listID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("创建请销点用户ID失败").toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        for (size_t i = 0; i < listID.size(); ++i)
        {
            StringMap wpUser{{"id", listID[i]}, {"workPointID", taskID}, {"userID", listUserID[i]}};
            listSql.push_back(ZGUtils::generateInsertSql("op_param_wp_user", wpUser));
        }
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务'%1'用户失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }

    bool ZGOPWPManagerMng::deleteWPTask(std::string taskID, ErrorInfo& e)
    {
        std::string stageID;
        if (!ZGProxyCommon::getDataByField("op_param_wp", taskID, "stageID", stageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取任务'%1'阶段失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (stageID != "ZG_WS_CREATE")
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务'%1'已提交，无法直接删除").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return removeTask(taskID, e);
    }

    bool ZGOPWPManagerMng::abolishWPTask(std::string taskID, ErrorInfo& e)
    {
        StringMap task{{"id", taskID}, {"stageID", "ZG_WS_ABOLISH"}};
        const auto& sql = ZGUtils::generateUpdateSql("op_param_wp", task);
        if (!ZGProxyCommon::execSql(sql))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("更新任务'%1'作废状态失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        StringMap taskExam;
        if (!ZGProxyCommon::getDataByFields("op_param_wp", taskID, {"requestWorkExamID", "finishWorkExamID"}, taskExam))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_RT);
            e.errDetail = QStringLiteral("获取任务'%1'审批ID失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (!saveTask(taskID, e))
            return false;
        if (!removeTask(taskID, e))
            return false;
        return true;
    }

    bool ZGOPWPManagerMng::confirmTask(std::string taskID, StringMap param, ErrorInfo& e)
    {
        QString sql = QString("SELECT * FROM op_param_wp WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::ListStringMap listTask;
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTask))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务'%1'信息失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listTask.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("找不到任务'%1'").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto& task = listTask[0];
        try
        {
            const auto& stageID = ZGUtils::get(task, "stageID");
            ZGLOG_INFO(QString("confirmTask: %1, stageID: %2").arg(taskID.c_str()).arg(stageID.c_str()).toStdString().c_str());
            if (stageID == "ZG_WS_CREATE")
                return onCreateConfirm(taskID, e);
            if (stageID == "ZG_WS_REQUEST_EXAM")
            {
                const auto& requestExamID = ZGUtils::get(task, "requestWorkExamID");
                return onRequestExamConfirm(taskID, requestExamID, e);
            }
            if (stageID == "ZG_WS_EXECUTE")
                return onExecuteConfirm(taskID, e);
            if (stageID == "ZG_WS_FINISH_EXAM")
            {
                const auto& finishExamID = ZGUtils::get(task, "finishWorkExamID");
                return onFinishExamConfirm(taskID, finishExamID, e);
            }
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("无效的任务阶段'%1'").arg(stageID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        catch (const std::exception& ex)
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = ex.what();
            ZGLOG_ERROR(e);
            return false;
        }
    }

    bool ZGOPWPManagerMng::backTask(std::string taskID, StringMap param, ErrorInfo& e)
    {
        QString sql = QString("SELECT stageID FROM op_param_wp WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::StringList listStageID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listStageID))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务'%1'当前阶段失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (listStageID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("任务'%1'不存在").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        const auto stageID = listStageID[0];
        std::string newStageID;
        if (stageID == "ZG_WS_REQUEST_EXAM")
            newStageID = "ZG_WS_CREATE";
        else if (stageID == "ZG_WS_FINISH_EXAM")
            newStageID = "ZG_WS_EXECUTE";
        else
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("错误的阶段'%1'").arg(stageID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        ZG6000::StringMap wp{ {"stageID", newStageID} };
        return editWPTask(taskID, wp, e);
    }

    bool ZGOPWPManagerMng::getWPUser(std::string taskID, ListStringMap& listUser, ErrorInfo& e)
    {
        QString sql = QString("SELECT a.userID AS id, b.name FROM op_param_wp_user a "
            "LEFT JOIN sp_param_hrm_user b ON a.userID = b.id "
            "WHERE a.workPointID = '%1' ORDER BY id").arg(taskID.c_str());
        if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUser))
        {
            e = ZGRuntime::instance()->getErrorInfo(::ZGOPWPManager::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取任务'%1'用户失败").arg(taskID.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        return true;
    }
} // namespace ZG6000

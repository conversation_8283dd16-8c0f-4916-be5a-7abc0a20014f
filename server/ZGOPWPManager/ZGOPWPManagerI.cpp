#include "ZGOPWPManagerI.h"
#include "ZGOPWPManagerMng.h"

namespace ZG6000 {

ZGOPWPManagerI::ZGOPWPManagerI()
{
    ZGOPWPManagerMng::instance()->init();
}

bool ZGOPWPManagerI::checkState(const Ice::Current& current)
{
    return ZGOPWPManagerMng::instance()->checkState();
}

void ZGOPWPManagerI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current& current)
{
    ZGOPWPManagerMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord));
}

bool ZGOPWPManagerI::getWPTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPWPManagerMng::instance()->getWPTaskList(std::move(param), listTask, e);
}

bool ZGOPWPManagerI::createWPTask(StringMap param, StringList listUserID, std::string &taskID, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPWPManagerMng::instance()->createWPTask(std::move(param), std::move(listUserID), taskID, e);
}

bool ZGOPWPManagerI::editWPTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPWPManagerMng::instance()->editWPTask(std::move(taskID), std::move(param), e);
}

bool ZGOPWPManagerI::editWPUser(std::string taskID, StringList listUserID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPWPManagerMng::instance()->editWPUser(std::move(taskID), std::move(listUserID), e);
}

bool ZGOPWPManagerI::deleteWPTask(std::string taskID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPWPManagerMng::instance()->deleteWPTask(std::move(taskID), e);
}

bool ZGOPWPManagerI::abolishWPTask(std::string taskID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPWPManagerMng::instance()->abolishWPTask(std::move(taskID), e);
}

bool ZGOPWPManagerI::confirmTask(std::string taskID, StringMap param, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPWPManagerMng::instance()->confirmTask(std::move(taskID), std::move(param), e);
}

bool ZGOPWPManagerI::backTask(std::string taskID, StringMap param, ErrorInfo& e, const Ice::Current& current)
{
    return ZGOPWPManagerMng::instance()->backTask(std::move(taskID), std::move(param), e);
}

bool ZGOPWPManagerI::getWPUser(std::string taskID, ListStringMap &listUser, ErrorInfo &e, const Ice::Current &current)
{
    return ZGOPWPManagerMng::instance()->getWPUser(std::move(taskID), listUser, e);
}

} // namespace ZG6000

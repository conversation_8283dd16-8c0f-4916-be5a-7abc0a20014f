#ifndef ZG6000_ZGOPWPMANAGERMNG_H
#define ZG6000_ZGOPWPMANAGERMNG_H

#include <QObject>
#include "ZGProxyCommon.h"

class ZGMqttClient;
namespace ZG6000 {

class ZGOPWPManagerMng : public QObject
{
    Q_OBJECT
public:
    static ZGOPWPManagerMng* instance();
    void init();
    bool checkState();
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord);
    /**
     * @brief 获取请销点任务列表
     * @param param 条件参数(condition: 条件字符串，order: 排序规则，sort: 排序字段，offset: 起始偏移, limit: 限制条数)
     * @param listTask 请销点任务列表
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool getWPTaskList(StringMap param, ListStringMap &listTask, ErrorInfo &e);

    /**
     * @brief 创建请销点任务
     * @param param 任务参数
     * @param listUserID 人员列表
     * @param taskID 创建的任务ID
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool createWPTask(StringMap param, StringList listUserID, std::string& taskID, ErrorInfo &e);

    /**
     * @brief   编辑请销点任务
     *
     * @param           taskID  任务ID.
     * @param           param   任务参数.
     * @param [in,out]  e       执行失败时的错误描述.
     *
     * @return  执行成功返回true，失败返回false.
     */
    bool editWPTask(std::string taskID, StringMap param, ErrorInfo &e);

    /**
     * @brief 编辑请销点人员
     * @param taskID 任务ID
     * @param listUserID 人员列表
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool editWPUser(std::string taskID, StringList listUserID, ErrorInfo& e);

    /**
     * @brief 删除请销点任务
     * @param taskID 任务ID
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool deleteWPTask(std::string taskID, ErrorInfo& e);

    /**
     * @brief 作废请销点任务
     * @param taskID 任务ID
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool abolishWPTask(std::string taskID, ErrorInfo& e);

    /**
     * @brief 确认请销点任务
     * @param taskID 任务ID
     * @param param 任务参数
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool confirmTask(std::string taskID, StringMap param, ErrorInfo &e);

    bool backTask(std::string taskID, StringMap param, ErrorInfo& e);

    /**
     * @brief 获取请销点人员
     * @param taskID 任务ID
     * @param listUser 人员列表
     * @param e 执行失败时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    bool getWPUser(std::string taskID, ListStringMap &listUser, ErrorInfo &e);

private:
    explicit ZGOPWPManagerMng(QObject* parent = nullptr);
    void initServerInstConfig();
    bool initServerInstInfo();
    bool initParams();
    bool initMqttClient();
    bool onCreateConfirm(const std::string& taskID, ErrorInfo& e);
    bool onRequestExamConfirm(const std::string& taskID, const std::string& examID, ErrorInfo& e);
    bool onExecuteConfirm(const std::string& taskID, ErrorInfo& e);
    bool onFinishExamConfirm(const std::string& taskID, const std::string& examID, ErrorInfo& e);
    bool examCall(const std::function<bool(std::shared_ptr<ZGSPExamManagerPrx>)>& func, ErrorInfo& e);
    bool finishExam(const std::string& examID, ErrorInfo& e);
    bool saveTask(const std::string& taskID, ErrorInfo& e);
    bool removeTask(const std::string& taskID, ErrorInfo& e);

private:
    bool m_initialized{false};
    QString m_serverName{""};
    QString m_instName{""};
    int m_initInterval{10};
    int m_checkInterval{10};
    std::string m_examID;
    StringMap m_mapAppNode;
    StringMap m_mapRegion;
    StringMap m_mapStage;
    ZGMqttClient* m_pMqttClient{ nullptr };
};

inline static ZGOPWPManagerMng* g_pInstance = nullptr;

} // namespace ZG6000

#endif // ZG6000_ZGOPWPMANAGERMNG_H

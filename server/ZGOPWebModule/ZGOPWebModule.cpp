#include "ZGOPWebModule.h"
#include "ZGOPHandle.h"

ZGOPWebModule::ZGOPWebModule(QObject *parent)
    : ZGWebModule(parent)
{
    m_pHandle = new ZGOPHandle(this);
    registerHandle("op/task/list", m_pHandle, &ZG<PERSON>Handle::on_task_list);
    registerHandle("op/ot/list", m_pHandle, &ZGOPHandle::on_ot_task_list);
    registerHandle("op/ot/count", m_pHandle, &ZGOPHandle::on_ot_task_count);
    registerHandle("op/ot/typical/list", m_pHandle, &ZGOPHandle::on_ot_typical_list);
    registerHandle("op/ot/template/list", m_pHandle, &ZGOPHandle::on_ot_template_list);
    registerHandle("op/ot/info", m_pHandle, &ZGOPHandle::on_ot_info);
    registerHandle("op/ot/create", m_pHandle, &ZGOPHandle::on_ot_task_create);
    registerHandle("op/ot/item/create", m_pHandle, &Z<PERSON><PERSON>Handle::on_ot_item_create);
    registerHandle("op/ot/item/delete", m_pHandle, &ZGOPHandle::on_ot_item_delete);
    registerHandle("op/ot/edit", m_pHandle, &ZGOPHandle::on_ot_task_edit);
    registerHandle("op/ot/delete", m_pHandle, &ZGOPHandle::on_ot_task_delete);
    registerHandle("op/ot/start", m_pHandle, &ZGOPHandle::on_ot_task_start);
    registerHandle("op/ot/pause", m_pHandle, &ZGOPHandle::on_ot_task_pause);
    registerHandle("op/ot/continue", m_pHandle, &ZGOPHandle::on_ot_task_resume);
    registerHandle("op/ot/retry", m_pHandle, &ZGOPHandle::on_ot_task_retry);
    registerHandle("op/ot/item/skip", m_pHandle, &ZGOPHandle::on_ot_item_skip);
    registerHandle("op/ot/abolish", m_pHandle, &ZGOPHandle::on_ot_task_abolish);
    registerHandle("op/ot/confirm", m_pHandle, &ZGOPHandle::on_ot_task_confirm);
    registerHandle("op/ot/convert", m_pHandle, &ZGOPHandle::on_ot_task_convert);
    registerHandle("op/ot/task/download", m_pHandle, &ZGOPHandle::on_ot_task_download);
    registerHandle("op/ot/task/update", m_pHandle, &ZGOPHandle::on_ot_task_update);
    registerHandle("op/ot/task/transfer", m_pHandle, &ZGOPHandle::on_ot_task_transfer);
    registerHandle("op/ot/item/update", m_pHandle, &ZGOPHandle::on_ot_item_update);
    registerHandle("op/ot/preview/start", m_pHandle, &ZGOPHandle::on_ot_preview_start);
    registerHandle("op/ot/preview/stop", m_pHandle, &ZGOPHandle::on_ot_preview_stop);
    registerHandle("op/ot/preview/pause", m_pHandle, &ZGOPHandle::on_ot_preview_pause);
    registerHandle("op/ot/preview/continue", m_pHandle, &ZGOPHandle::on_ot_preview_resume);
    registerHandle("op/ot/preview/retry", m_pHandle, &ZGOPHandle::on_ot_preview_retry);
    registerHandle("op/ot/preview/confirm", m_pHandle, &ZGOPHandle::on_ot_preview_confirm);
    registerHandle("op/ot/device/term/get", m_pHandle, &ZGOPHandle::on_ot_device_term_get);
    registerHandle("op/ot/common/term/get", m_pHandle, &ZGOPHandle::on_ot_common_term_get);
    registerHandle("op/ot/term/rule/get", m_pHandle, &ZGOPHandle::on_ot_term_rule_get);
    registerHandle("op/event/get", m_pHandle, &ZGOPHandle::on_event_get);
    registerHandle("op/it/list", m_pHandle, &ZGOPHandle::on_it_task_list);
    registerHandle("op/it/task/typical/list", m_pHandle, &ZGOPHandle::on_it_task_typical_list);
    registerHandle("op/it/count", m_pHandle, &ZGOPHandle::on_it_task_count);
    registerHandle("op/it/convert", m_pHandle, &ZGOPHandle::on_it_task_convert);
    registerHandle("op/it/info", m_pHandle, &ZGOPHandle::on_it_info);
    registerHandle("op/it/item/info", m_pHandle, &ZGOPHandle::on_it_item_info);
    registerHandle("op/it/task/download", m_pHandle, &ZGOPHandle::on_it_task_download);
    registerHandle("op/it/tasktype/object/get", m_pHandle, &ZGOPHandle::on_it_tasktype_object_get);
    registerHandle("op/it/task/typical/create", m_pHandle, &ZGOPHandle::on_it_task_typical_create);
    registerHandle("op/it/task/special/create", m_pHandle, &ZGOPHandle::on_it_task_special_create);
    registerHandle("op/it/task/uav/create", m_pHandle, &ZGOPHandle::on_it_task_uav_create);
    registerHandle("op/it/task/edit", m_pHandle, &ZGOPHandle::on_it_task_edit);
    registerHandle("op/it/task/delete", m_pHandle, &ZGOPHandle::on_it_task_delete);
    registerHandle("op/it/task/start", m_pHandle, &ZGOPHandle::on_it_task_start);
    registerHandle("op/it/task/pause", m_pHandle, &ZGOPHandle::on_it_task_pause);
    registerHandle("op/it/task/resume", m_pHandle, &ZGOPHandle::on_it_task_resume);
    registerHandle("op/it/task/retry", m_pHandle, &ZGOPHandle::on_it_task_retry);
    registerHandle("op/it/task/abolish", m_pHandle, &ZGOPHandle::on_it_task_abolish);
    registerHandle("op/it/task/confirm", m_pHandle, &ZGOPHandle::on_it_task_confirm);
    registerHandle("op/it/task/update", m_pHandle, &ZGOPHandle::on_it_task_update);
    registerHandle("op/it/item/update", m_pHandle, &ZGOPHandle::on_it_item_update);
    registerHandle("op/it/action/update", m_pHandle, &ZGOPHandle::on_it_action_update);
    registerHandle("op/it/preset/ctrl", m_pHandle, &ZGOPHandle::on_it_preset_ctrl);
    registerHandle("op/it/action/yv/get", m_pHandle, &ZGOPHandle::on_it_action_yv_get);
    registerHandle("op/video/record", m_pHandle, &ZGOPHandle::on_video_record);
    registerHandle("op/image/capture", m_pHandle, &ZGOPHandle::on_image_capture);
    registerHandle("op/iu/task/download", m_pHandle, &ZGOPHandle::on_iu_task_download);
    registerHandle("op/iu/task/temporary/create", m_pHandle, &ZGOPHandle::on_iu_task_temporary_create);
    registerHandle("op/iu/task/template/create", m_pHandle, &ZGOPHandle::on_iu_task_template_create);
    registerHandle("op/iu/task/update", m_pHandle, &ZGOPHandle::on_iu_task_update);
    registerHandle("op/iu/item/update", m_pHandle, &ZGOPHandle::on_iu_item_update);
    registerHandle("op/wp/task/list", m_pHandle, &ZGOPHandle::on_wp_task_list);
    registerHandle("op/wp/task/user/list", m_pHandle, &ZGOPHandle::on_wp_task_user_list);
    registerHandle("op/wp/task/create", m_pHandle, &ZGOPHandle::on_wp_task_create);
    registerHandle("op/wp/task/edit", m_pHandle, &ZGOPHandle::on_wp_task_edit);
    registerHandle("op/wp/task/user/edit", m_pHandle, &ZGOPHandle::on_wp_task_user_edit);
    registerHandle("op/wp/task/submit", m_pHandle, &ZGOPHandle::on_wp_task_confirm);
    registerHandle("op/wp/task/back", m_pHandle, &ZGOPHandle::on_wp_task_back);
    registerHandle("op/wp/task/delete", m_pHandle, &ZGOPHandle::on_wp_task_delete);
    registerHandle("op/wp/task/abolish", m_pHandle, &ZGOPHandle::on_wp_task_abolish);
    registerHandle("op/outage/device/get", m_pHandle, &ZGOPHandle::on_outage_device_get);
    registerHandle("op/outage/task/list", m_pHandle, &ZGOPHandle::on_outage_task_list);
    registerHandle("op/outage/task/count", m_pHandle, &ZGOPHandle::on_outage_task_count);
    registerHandle("op/outage/typical/list", m_pHandle, &ZGOPHandle::on_outage_typical_list);
    registerHandle("op/outage/typical/delete", m_pHandle, &ZGOPHandle::on_outage_typical_delete);
    registerHandle("op/outage/task/create", m_pHandle, &ZGOPHandle::on_outage_task_create);
    registerHandle("op/outage/task/edit", m_pHandle, &ZGOPHandle::on_outage_task_edit);
    registerHandle("op/outage/task/delete", m_pHandle, &ZGOPHandle::on_outage_task_delete);
    registerHandle("op/outage/task/abolish", m_pHandle, &ZGOPHandle::on_outage_task_abolish);
    registerHandle("op/outage/task/cancel", m_pHandle, &ZGOPHandle::on_outage_task_cancel);
    registerHandle("op/outage/task/info", m_pHandle, &ZGOPHandle::on_outage_task_info);
    registerHandle("op/outage/task/confirm", m_pHandle, &ZGOPHandle::on_outage_task_confirm);
    registerHandle("op/outage/task/start", m_pHandle, &ZGOPHandle::on_outage_task_start);
    registerHandle("op/outage/task/lock", m_pHandle, &ZGOPHandle::on_outage_task_lock);
    registerHandle("op/outage/task/unlock", m_pHandle, &ZGOPHandle::on_outage_task_unlock);
    registerHandle("op/outage/task/convert", m_pHandle, &ZGOPHandle::on_outage_task_convert);
    registerHandle("op/outage/task/move", m_pHandle, &ZGOPHandle::on_outage_task_move);
    registerHandle("op/outage/isolator/lock", m_pHandle, &ZGOPHandle::on_outage_isolator_lock);
    registerHandle("op/outage/isolator/lock/batch", m_pHandle, &ZGOPHandle::on_outage_isolator_lock_batch);
    registerHandle("op/outage/isolator/unlock", m_pHandle, &ZGOPHandle::on_outage_isolator_unlock);
    registerHandle("op/outage/isolator/unlock/batch", m_pHandle, &ZGOPHandle::on_outage_isolator_unlock_batch);
    registerHandle("op/outage/switch/lock", m_pHandle, &ZGOPHandle::on_outage_switch_lock);
    registerHandle("op/outage/switch/lock/batch", m_pHandle, &ZGOPHandle::on_outage_switch_lock_batch);
    registerHandle("op/outage/switch/unlock", m_pHandle, &ZGOPHandle::on_outage_switch_unlock);
    registerHandle("op/outage/switch/unlock/batch", m_pHandle, &ZGOPHandle::on_outage_switch_unlock_batch);
    registerHandle("op/outage/switch/close", m_pHandle, &ZGOPHandle::on_outage_switch_close);
    registerHandle("op/outage/switch/open", m_pHandle, &ZGOPHandle::on_outage_switch_open);
    registerHandle("op/outage/otp/send", m_pHandle, &ZGOPHandle::on_outage_otp_send);
    registerHandle("op/outage/phone/change", m_pHandle, &ZGOPHandle::on_outage_phone_change);
    registerHandle("op/outage/ptw/apply", m_pHandle, &ZGOPHandle::on_outage_ptw_apply);
    registerHandle("op/outage/sft/apply", m_pHandle, &ZGOPHandle::on_outage_sft_apply);
    registerHandle("op/outage/ptw/cancel", m_pHandle, &ZGOPHandle::on_outage_ptw_cancel);
    registerHandle("op/outage/sft/cancel", m_pHandle, &ZGOPHandle::on_outage_sft_cancel);
    registerHandle("op/outage/otp/save", m_pHandle, &ZGOPHandle::on_outage_otp_save);
    registerHandle("op/outage/device/monitor", m_pHandle, &ZGOPHandle::on_outage_device_monitor);
    registerHandle("op/outage/confirm", m_pHandle, &ZGOPHandle::on_outage_confirm);
    registerHandle("op/outage/event/save", m_pHandle, &ZGOPHandle::on_outage_event_save);
    registerHandle("op/outage/group/device/get", m_pHandle, &ZGOPHandle::on_outage_group_device_get);
    registerHandle("op/outage/lockdevice/password/set", m_pHandle, &ZGOPHandle::on_outage_lock_device_password_set);
    registerHandle("op/outage/isolator/normalstate/set", m_pHandle, &ZGOPHandle::on_outage_isolator_normalstate_set);
    registerHandle("op/outage/externallock/enable", m_pHandle, &ZGOPHandle::on_outage_lock_device_password_set);
    registerHandle("op/outage/unlock/externallock", m_pHandle, &ZGOPHandle::on_outage_unlock_external_lock);
    registerHandle("op/outage/lock/externallock", m_pHandle, &ZGOPHandle::on_outage_lock_external_lock);
    registerHandle("op/outage/close/condition/get", m_pHandle, &ZGOPHandle::on_outage_close_condition_get);
    registerHandle("op/outage/otp/get", m_pHandle, &ZGOPHandle::on_outage_otp_get);
}

bool ZGOPWebModule::initialize()
{
    if (!m_pHandle->initialize())
        return false;
    return true;
}

QString ZGOPWebModule::prefix()
{
    return "op";
}


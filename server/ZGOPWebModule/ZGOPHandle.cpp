#include "ZGOPHandle.h"

#include <QJsonDocument>

#include "ZGJson.h"
#include "ZGRuntime.h"
#include "ZGSecure.h"
#include "ZGUtils.h"
#include "ZGWebModule.h"

ZGOPHandle::ZGOPHandle(QObject* parent)
    : QObject{parent}
{
}

bool ZGOPHandle::initialize()
{
    std::string sql = "SELECT id, color FROM sp_dict_alarm_level";
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql(sql, listRecord))
    {
        ZGLOG_ERROR(QStringLiteral("获取告警级别失败"));
        return false;
    }
    for (const auto& record : listRecord)
    {
        m_mapAlarmLevelColor.insert(std::make_pair(record.at("id"), record.at("color")));
    }
    sql = "SELECT id, name FROM sp_dict_operator";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapOperator))
    {
        ZGLOG_ERROR(u8"获取操作符表信息失败");
        return false;
    }
    sql = "SELECT id, name FROM mp_dict_data_category";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDataCategory))
    {
        ZGLOG_ERROR(u8"获取数据类别失败");
        return false;
    }
    sql = "SELECT CONCAT(dataCategoryID, '/', propValue) AS id, propName AS name FROM mp_param_data_category_property";
    if (!ZGProxyCommon::execQuerySqlPair(sql, m_mapDataCategoryProperty))
    {
        ZGLOG_ERROR(u8"获取数据类别属性失败");
        return false;
    }
    return true;
}

ZGWebModule::Response ZGOPHandle::on_task_list(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    const auto& condition = object.find("condition") != object.end() ? object["condition"].toString() : "1=1";
    const auto& orderType = object.find("order") != object.end() ? object["order"].toString() : "ASC";
    const auto& orderField = object.find("sort") != object.end() ? object["sort"].toString() : "id";
    const auto& offset = object.find("offset") != object.end() ? object["offset"].toInt() : 0;
    const auto& limit = object.find("limit") != object.end() ? object["limit"].toInt() : 1000;
    QString addition = QString(" ORDER BY %1 %2 LIMIT %3, %4").arg(orderField)
                                                              .arg(orderType).arg(offset).arg(limit);
    QString sql = QString("SELECT * FROM op_param_task a "
        "WHERE %1").arg(condition) + addition;
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBData();
    if (dbProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取数据服务代理对象失败"));
    try
    {
        ZG6000::ErrorInfo e;
        ZG6000::ListStringMap listTask;
        if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listTask);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::on_ot_task_create(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"typeID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap mapParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            mapParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        std::string otID;
        ZG6000::ErrorInfo e;
        if (!taskProxy->createOT(mapParam["typeID"], mapParam, otID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(otID.c_str());
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_item_create(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap createParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            createParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ListStringMap items;
        ZG6000::ErrorInfo e;
        if (!taskProxy->createOtItem(taskID, createParam, items, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(items);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_item_delete(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap deleteParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            deleteParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->deleteOtItem(taskID, deleteParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_list(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap queryParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            queryParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ListStringMap listTask;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskList(queryParam, listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listTask);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_typical_list(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap queryParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            queryParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ListStringMap listTask;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskList(queryParam, listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        ZG6000::ListStringMap listOt;
        for (auto& task : listTask)
        {
            std::string typeID;
            if (!ZGProxyCommon::getDataByField("op_param_ot", task["id"], "typeID", typeID))
                continue;
            if (typeID == "ZG_OT_TYPICAL" && task["rtTaskStageID"] == "ZG_TS_INIT")
                listOt.push_back(std::move(task));
        }
        const auto& json = ZGJson::convertToJson(listOt);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_template_list(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"appNodeID", "subsystemID", "majorID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    ZG6000::StringMap queryParam;
    const auto& appNodeID = object["appNodeID"].toString();
    queryParam["a.appNodeID"] = appNodeID.toStdString();
    const auto& subsystemID = object["subsystemID"].toString();
    queryParam["a.subsystemID"] = subsystemID.toStdString();
    const auto& majorID = object["majorID"].toString();
    queryParam["a.majorID"] = majorID.toStdString();
    if (object.contains("destTypeID"))
    {
        const auto& destTypeID = object["destTypeID"].toString().toStdString();
        queryParam["b.destTypeID"] = destTypeID;
    }
    // 根据queryParam拼接查询条件
    ZG6000::StringList listCondition;
    for (const auto& [field, value] : queryParam)
    {
        listCondition.push_back(field + " = '" + value + "'");
    }
    const auto& condition = ZGUtils::join(listCondition, " AND ");
    QString sql = QString("SELECT a.otTemplateID AS id, b.name, b.execTermID FROM op_param_appnode_ot_template a LEFT JOIN op_param_ot_template b "
        "ON a.otTemplateID = b.id LEFT JOIN sp_param_appnode c ON a.appNodeID = c.id "
        "WHERE %1 ORDER BY id").arg(condition.c_str());
    int checkRule = 1;
    if (object.contains("checkRule"))
        checkRule = object["checkRule"].toVariant().toInt();
    ZG6000::ListStringMap listTemplate, listTemplateNew;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTemplate))
        return ZGWebModule::errorObject("获取模板票列表失败");
    auto ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
    if (ruleProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取规则引擎服务代理对象失败"));
    try
    {
        for (auto& tpl : listTemplate)
        {
            if (checkRule == 1)
            {
                if (!tpl["execTermID"].empty())
                {
                    ZG6000::ErrorInfo e;
                    if (!ruleProxy->checkAppNodeExecCondition(appNodeID.toStdString(), tpl["execTermID"], 0, e))
                        continue;
                }
            }
            listTemplateNew.push_back(tpl);
        }
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
    std::string appNodeName;
    ZGProxyCommon::getDataByField("sp_param_appnode", appNodeID.toStdString(), "name", appNodeName);
    for (auto& tpl : listTemplateNew)
    {
        ZGUtils::replaceString(tpl["name"], "[appNode]", appNodeName);
    }
    const auto& json = ZGJson::convertToJson(listTemplateNew);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGOPHandle::on_ot_task_count(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"condition"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& condition = object["condition"].toString();
    QString sql = QString("SELECT COUNT(*) FROM op_param_task a WHERE %1").arg(condition);
    std::string taskCount;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), taskCount))
        return ZGWebModule::errorObject(u8"获取任务数量失败");
    return ZGWebModule::replyObject(taskCount.c_str());
}

ZGWebModule::Response ZGOPHandle::on_ot_task_edit(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"id", "head", "items"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& otID = object["id"].toString().toStdString();
        auto headObj = object["head"].toObject();
        ZG6000::StringMap head;
        for (auto it = headObj.begin(); it != headObj.end(); ++it)
        {
            head[it.key().toStdString()] = it.value().toString().toStdString();
        }
        const auto& itemArray = object["items"].toArray();
        ZG6000::ListStringMap items;
        for (auto it = itemArray.begin(); it != itemArray.end(); ++it)
        {
            const auto& itemObj = it->toObject();
            ZG6000::StringMap item;
            for (auto itItem = itemObj.begin(); itItem != itemObj.end(); ++itItem)
            {
                item[itItem.key().toStdString()] = itItem.value().toString().toStdString();
            }
            items.push_back(std::move(item));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->editOT(otID, head, items, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_delete(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->deleteTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_info(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req)
{
    const auto& otID = param.toString().toStdString();
    if (otID.empty())
        return ZGWebModule::errorObject(QStringLiteral("任务ID不能为空"));
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        ZG6000::StringMap head;
        ZG6000::ListStringMap items;
        if (!taskProxy->getOT(otID, head, items, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonObject obj;
        QJsonObject headObj;
        for (const auto& pair : head)
        {
            headObj[pair.first.c_str()] = pair.second.c_str();
        }
        obj["head"] = headObj;
        QJsonArray itemArray;
        for (const auto& item : items)
        {
            QJsonObject itemObj;
            for (const auto& pair : item)
            {
                itemObj[pair.first.c_str()] = pair.second.c_str();
            }
            itemArray.push_back(itemObj);
        }
        obj["items"] = itemArray;
        QJsonDocument doc(obj);
        return ZGWebModule::replyObject(doc.object());
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_start(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->startTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_pause(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->pauseTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_resume(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->resumeTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_retry(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->retryTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_item_skip(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "skipUserID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    ZGLOG_INFO(QString("skip item, taskID: %1, skipUserID: %2").arg(object["taskID"].toString()).arg(object["skipUserID"].toString()));
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->skipItem(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_abolish(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->abolishTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_confirm(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->confirmTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_convert(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->convertOT(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_download(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& array = param.toArray();
        ZG6000::StringList listTaskID;
        for (auto taskRef : array)
        {
            listTaskID.push_back(taskRef.toString().toStdString());
        }
        ZG6000::ErrorInfo e;
        ZG6000::ListStringMap listTask;
        ZG6000::ListStringMap listItem;
        if (!taskProxy->downloadTask(clientID.toStdString(), listTaskID, listTask, listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonObject object;
        QJsonArray taskArray, itemArray;
        for (const auto& task : listTask)
        {
            QJsonObject taskObject;
            for (const auto& [field, value] : task)
            {
                taskObject[field.c_str()] = value.c_str();
            }
            taskArray.push_back(taskObject);
        }
        for (const auto& item : listItem)
        {
            QJsonObject itemObject;
            for (const auto& [field, value] : item)
            {
                itemObject[field.c_str()] = value.c_str();
            }
            itemArray.push_back(itemObject);
        }
        object["task"] = taskArray;
        object["item"] = itemArray;
        return ZGWebModule::replyObject(object);
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_update(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& array = param.toArray();
        ZG6000::ListStringMap listTask;
        for (auto taskRef : array)
        {
            const auto& taskObj = taskRef.toObject();
            ZG6000::StringMap task;
            for (auto it = taskObj.begin(); it != taskObj.end(); ++it)
            {
                task[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listTask.push_back(std::move(task));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->updateTask(listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_task_transfer(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "clientID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& rtClientID = object["clientID"].toString().toStdString();
        ZG6000::StringMap task{{"id", taskID}, {"rtClientID", rtClientID}};
        ZG6000::ErrorInfo e;
        if (!taskProxy->updateTask({task}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_item_update(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& itemArray = param.toArray();
        ZG6000::ListStringMap listItem;
        for (auto itemRef : itemArray)
        {
            const auto& itemObj = itemRef.toObject();
            ZG6000::StringMap item;
            for (auto it = itemObj.begin(); it != itemObj.end(); ++it)
            {
                item[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
                ZGLOG_INFO(QString("%1:%2").arg(it.key()).arg(it.value().toVariant().toString()));
            }
            listItem.push_back(std::move(item));
        }
        ZG6000::ErrorInfo e;
        ZGLOG_INFO(QString("updateItem, id: %1").arg(listItem[0]["id"].c_str()));
        if (!taskProxy->updateItem(listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        ZGLOG_INFO(QString("updateItem success, id: %1").arg(listItem[0]["id"].c_str()));
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_preview_start(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->startPreview(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_preview_stop(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->stopPreview(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_preview_pause(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->pausePreview(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_preview_resume(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->resumePreview(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_preview_retry(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->retryPreview(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_preview_confirm(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap taskParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            taskParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->confirmPreview(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_ot_device_term_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    QString sql = QString("SELECT a.id, a.name FROM mp_param_term a LEFT JOIN mp_dict_device_subtype b "
        "ON a.deviceSubtypeID = b.id LEFT JOIN mp_param_device c "
        "ON b.id = c.subtypeID WHERE c.id = '%1' ORDER BY a.id").arg(object["deviceID"].toString());
    ZG6000::ListStringMap listTerm;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTerm))
        return ZGWebModule::errorObject(QStringLiteral("获取设备操作术语失败"));
    const auto& json = ZGJson::convertToJson(listTerm);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGOPHandle::on_ot_common_term_get(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    QString sql = QString("SELECT id, name FROM mp_param_term WHERE deviceSubtypeID IS NULL OR deviceSubtypeID = ''");
    ZG6000::ListStringMap listTerm;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listTerm))
        return ZGWebModule::errorObject(QStringLiteral("获取公共操作术语失败"));
    const auto& json = ZGJson::convertToJson(listTerm);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.array());
}

ZGWebModule::Response ZGOPHandle::on_ot_term_rule_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"stepID", "conditionTypeID", "simFlag"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& stepID = object["stepID"].toString().toStdString();
    const auto& conditionTypeID = object["conditionTypeID"].toString().toStdString();
    const auto& simFlag = object["simFlag"].toString().toInt();
    ZG6000::StringMap stepParam;
    if (!ZGProxyCommon::getDataByFields("op_param_ot_item", stepID, {"otID", "termID", "termItemID", "deviceID"}, stepParam))
        return ZGWebModule::errorObject(QStringLiteral("获取操作票步骤'%1'参数失败").arg(stepID.c_str()));
    try
    {
        const auto& otID = ZGUtils::get(stepParam, "otID");
        const auto& termID = ZGUtils::get(stepParam, "termID");
        const auto& termItemID = ZGUtils::get(stepParam, "termItemID");
        const auto& deviceID = ZGUtils::get(stepParam, "deviceID");
        ZG6000::StringMap termParam, termItemParam;
        if (!ZGProxyCommon::getDataByFields("op_param_ot_term_item", termItemID, {"isCheckExecCondition", "isCheckConfirmCondition"}, termItemParam))
            return ZGWebModule::errorObject(QStringLiteral("获取操作术语项'%1'参数失败").arg(termItemID.c_str()));
        if (!ZGProxyCommon::getDataByFields("mp_param_term", termID, {"deviceSubtypeID"}, termParam))
            return ZGWebModule::errorObject(QStringLiteral("获取操作术语'%1'参数失败").arg(termID.c_str()));
        const auto& isCheckExecCondition = ZGUtils::get(termItemParam, "isCheckExecCondition");
        const auto& isCheckConfirmCondition = ZGUtils::get(termItemParam, "isCheckConfirmCondition");
        auto ruleProxy = ZGProxyMng::instance()->getProxyMPRuleEngine();
        if (ruleProxy == nullptr)
            return ZGWebModule::errorObject(QStringLiteral("获取规则引擎服务代理对象失败"));
        QJsonArray array;
        ZG6000::ListStringMap listCondition;
        ZG6000::ErrorInfo e;
        if (conditionTypeID == "ZG_CT_EXEC")
        {
            if (isCheckExecCondition != "1")
                return ZGWebModule::replyObject(array);
            if (!deviceID.empty())
            {
                if (!ruleProxy->getDeviceExecCondition(deviceID, termID, simFlag, listCondition, e))
                    return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
            else
            {
                const auto& deviceSubTypeID = ZGUtils::get(termParam, "deviceSubtypeID");
                if (!deviceSubTypeID.empty())
                    return ZGWebModule::errorObject(QStringLiteral("操作术语配置了子类型，但没有实例化设备"));
                std::string appNodeID;
                if (!ZGProxyCommon::getDataByField("op_param_task", otID, "appNodeID", appNodeID))
                    return ZGWebModule::errorObject(QStringLiteral("获取操作票'%1'所属应用节点失败").arg(otID.c_str()));
                if (!ruleProxy->getAppNodeExecCondition(appNodeID, termID, simFlag, listCondition, e))
                    return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
        }
        if (conditionTypeID == "ZG_CT_CONFIRM")
        {
            if (isCheckConfirmCondition != "1")
                return ZGWebModule::replyObject(array);
            if (!deviceID.empty())
            {
                if (!ruleProxy->getDeviceConfirmCondition(deviceID, termID, simFlag, listCondition, e))
                    return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
            else
            {
                const auto& deviceSubTypeID = ZGUtils::get(termParam, "deviceSubtypeID");
                if (!deviceSubTypeID.empty())
                    return ZGWebModule::errorObject(QStringLiteral("操作术语配置了子类型，但没有实例化设备"));
                std::string appNodeID;
                if (!ZGProxyCommon::getDataByField("op_param_task", otID, "appNodeID", appNodeID))
                    return ZGWebModule::errorObject(QStringLiteral("获取操作票'%1'所属应用节点失败").arg(otID.c_str()));
                if (!ruleProxy->getAppNodeConfirmCondition(appNodeID, termID, simFlag, listCondition, e))
                    return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            }
        }
        return convertRuleItem(listCondition);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
    catch (const std::exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::on_it_task_list(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap queryParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            queryParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ListStringMap listTask;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskList(queryParam, listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listTask);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_typical_list(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return otCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskOTPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap queryParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            queryParam[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ListStringMap listTask;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskList(queryParam, listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        ZG6000::ListStringMap listOt;
        for (auto& task : listTask)
        {
            std::string typeID;
            if (!ZGProxyCommon::getDataByField("op_param_it_task", task["id"], "typeID", typeID))
                continue;
            if (typeID == "ZG_TT_TYPICAL" && task["rtTaskStageID"] == "ZG_TS_INIT")
                listOt.push_back(std::move(task));
        }
        const auto& json = ZGJson::convertToJson(listOt);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_count(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"condition"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& condition = object["condition"].toString();
    QString sql = QString("SELECT COUNT(*) FROM op_param_task a WHERE %1").arg(condition);
    std::string deviceCount;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), deviceCount))
        return ZGWebModule::errorObject(u8"获取任务数量失败");
    return ZGWebModule::replyObject(deviceCount.c_str());
}

ZGWebModule::Response ZGOPHandle::on_it_info(const QString& clientID,
                                           const QVariantMap& headers,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req)
{
    const auto& itID = param.toString().toStdString();
    if (itID.empty())
        return ZGWebModule::errorObject(QStringLiteral("任务ID不能为空"));
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        ZG6000::StringMap head;
        ZG6000::ListStringMap items;
        ZGLOG_TRACE("on_it_info begin invoke");
        if (!taskProxy->getTaskItems(itID, head, items, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        ZGLOG_TRACE("on_it_info end invoke");
        QJsonObject obj;
        QJsonObject headObj;
        for (const auto& pair : head)
        {
            headObj[pair.first.c_str()] = pair.second.c_str();
        }
        obj["head"] = headObj;
        QJsonArray itemArray;
        for (const auto& item : items)
        {
            QJsonObject itemObj;
            for (const auto& pair : item)
            {
                itemObj[pair.first.c_str()] = pair.second.c_str();
            }
            itemArray.push_back(itemObj);
        }
        obj["items"] = itemArray;
        QJsonDocument doc(obj);
        return ZGWebModule::replyObject(doc.object());
    });
}

ZGWebModule::Response ZGOPHandle::on_it_item_info(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& itemID = param.toString().toStdString();
    if (itemID.empty())
        return ZGWebModule::errorObject(QStringLiteral("ID不能为空"));
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::ErrorInfo e;
        ZG6000::StringMap head;
        ZG6000::ListStringMap actions;
        if (!taskProxy->getItemActions(itemID, head, actions, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonObject obj;
        QJsonObject headObj;
        for (const auto& pair : head)
        {
            headObj[pair.first.c_str()] = pair.second.c_str();
        }
        obj["head"] = headObj;
        QJsonArray actionArray;
        for (const auto& item : actions)
        {
            QJsonObject actionObj;
            for (const auto& pair : item)
            {
                actionObj[pair.first.c_str()] = pair.second.c_str();
            }
            actionArray.push_back(actionObj);
        }
        obj["actions"] = actionArray;
        QJsonDocument doc(obj);
        return ZGWebModule::replyObject(doc.object());
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_download(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& array = param.toArray();
        ZG6000::StringList listTaskID;
        for (auto it = array.begin(); it != array.end(); ++it)
        {
            listTaskID.push_back(it->toString().toStdString());
        }
        ZG6000::ListStringMap listTask, listItem, listAction;
        ZG6000::ErrorInfo e;
        if (!taskProxy->downloadTask(listTaskID, listTask, listItem, listAction, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonObject object;
        QJsonArray taskArray, itemArray, actionArray;
        for (const auto& task : listTask)
        {
            QJsonObject taskObject;
            for (const auto& [field, value] : task)
            {
                taskObject[field.c_str()] = value.c_str();
            }
            taskArray.push_back(taskObject);
        }
        for (const auto& item : listItem)
        {
            QJsonObject itemObject;
            for (const auto& [field, value] : item)
            {
                itemObject[field.c_str()] = value.c_str();
            }
            itemArray.push_back(itemObject);
        }
        for (const auto& action : listAction)
        {
            QJsonObject actionObject;
            for (const auto& [field, value] : action)
            {
                actionObject[field.c_str()] = value.c_str();
            }
            actionArray.push_back(actionObject);
        }
        object["task"] = taskArray;
        object["item"] = itemArray;
        object["action"] = actionArray;
        return ZGWebModule::replyObject(object);
    });
}

ZGWebModule::Response ZGOPHandle::on_it_tasktype_object_get(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskTypeID", "appNodeID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskTypeID = object["taskTypeID"].toString().toStdString();
        const auto& appNodeID = object["appNodeID"].toString().toStdString();
        ZG6000::ListStringMap listObjects;
        ZG6000::ErrorInfo e;
        ZG6000::StringMap params{{"taskTypeID", taskTypeID}, {"appNodeID", appNodeID}};
        if (!taskProxy->getTaskTypeObjects(params, listObjects, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listObjects);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_typical_create(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "rtCreateUserID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->createTypicalTask(params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_special_create(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskTypeID", "objects", "params"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskTypeID = object["taskTypeID"].toString().toStdString();
        const auto& deviceArray = object["objects"].toArray();
        ZG6000::StringList listObjectID;
        for (auto deviceRef : deviceArray)
            listObjectID.push_back(deviceRef.toString().toStdString());
        ZG6000::StringMap mapParam;
        const auto& paramObj = object["params"].toObject();
        for (auto it = paramObj.begin(); it != paramObj.end(); ++it)
        {
            mapParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        std::string taskID;
        ZG6000::ErrorInfo e;
        if (!taskProxy->createSpecialTask(taskTypeID, listObjectID, mapParam, taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(taskID.c_str());
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_uav_create(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"preset", "task", "item", "action"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap task;
        const auto& taskObj = object["task"].toObject();
        for (auto it = taskObj.begin(); it != taskObj.end(); ++it)
        {
            task[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        const auto& presetArray = object["preset"].toArray();
        ZG6000::ListStringMap listPreset;
        for (auto presetRef : presetArray)
        {
            ZG6000::StringMap preset;
            const auto& presetObj = presetRef.toObject();
            for (auto it = presetObj.begin(); it != presetObj.end(); ++it)
            {
                preset[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listPreset.push_back(preset);
        }
        const auto& itemArray = object["item"].toArray();
        ZG6000::ListStringMap listTaskItem;
        for (auto itemRef : itemArray)
        {
            ZG6000::StringMap item;
            const auto& itemObj = itemRef.toObject();
            for (auto it = itemObj.begin(); it != itemObj.end(); ++it)
            {
                item[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listTaskItem.push_back(item);
        }
        const auto& actionArray = object["action"].toArray();
        ZG6000::ListStringMap listTaskItemAction;
        for (auto actionRef : actionArray)
        {
            ZG6000::StringMap action;
            const auto& actionObj = actionRef.toObject();
            for (auto it = actionObj.begin(); it != actionObj.end(); ++it)
            {
                action[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listTaskItemAction.push_back(action);
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->createUAVTask(task, listPreset, listTaskItem, listTaskItemAction, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_edit(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "head", "items"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& headObj = object["head"].toObject();
        ZG6000::StringMap head;
        for (auto it = headObj.begin(); it != headObj.end(); ++it)
        {
            head[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        const auto& itemArray = object["items"].toArray();
        ZG6000::ListStringMap listItem;
        for (auto itemRef : itemArray)
        {
            const auto& itemObj = itemRef.toObject();
            ZG6000::StringMap item;
            for (auto it = itemObj.begin(); it != itemObj.end(); ++it)
            {
                item[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listItem.push_back(std::move(item));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->editTask(taskID, head, listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_delete(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->deleteTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_start(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->startTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_pause(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->pauseTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_resume(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->resumeTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_retry(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->retryTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_abolish(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->abolishTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_confirm(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->confirmTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_convert(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID", "operator", "monitor"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->convertTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_task_update(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& array = param.toArray();
        ZG6000::ListStringMap listTask;
        for (auto taskRef : array)
        {
            const auto& taskObj = taskRef.toObject();
            ZG6000::StringMap task;
            for (auto it = taskObj.begin(); it != taskObj.end(); ++it)
            {
                task[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listTask.push_back(std::move(task));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->updateTask(listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_item_update(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& array = param.toArray();
        ZG6000::ListStringMap listItem;
        for (auto itemRef : array)
        {
            const auto& itemObj = itemRef.toObject();
            ZG6000::StringMap item;
            for (auto it = itemObj.begin(); it != itemObj.end(); ++it)
            {
                item[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listItem.push_back(std::move(item));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->updateItem(listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_action_update(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return itCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskITPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& array = param.toArray();
        ZG6000::ListStringMap listAction;
        for (auto actionRef : array)
        {
            const auto& actionObj = actionRef.toObject();
            ZG6000::StringMap action;
            for (auto it = actionObj.begin(); it != actionObj.end(); ++it)
            {
                action[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listAction.push_back(std::move(action));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->updateItem(listAction, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_it_action_yv_get(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& actionID = param.toString().toStdString();
    ZG6000::StringMap actionParam;
    if (!ZGProxyCommon::getDataByFields("op_param_it_task_action", actionID, {"actionTypeID", "presetID"}, actionParam))
        return ZGWebModule::errorObject(QStringLiteral("获取动作'%1'预置位失败").arg(actionID.c_str()));
    if ((actionParam["actionTypeID"] != "ZG_AT_HK_PHOTO") && (actionParam["actionTypeID"] != "ZG_AT_HK_VIDEO"))
        return ZGWebModule::replyObject("");
    ZG6000::StringMap presetParam;
    if (!ZGProxyCommon::getDataByFields("mp_param_preset", actionParam["presetID"], {"deviceID", "propertyName"}, presetParam))
        return ZGWebModule::errorObject(QStringLiteral("获取预置位'%1'参数失败").arg(actionParam["presetID"].c_str()));
    std::string tableName, dataID;
    ZG6000::StringMap property;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getProperty(presetParam["deviceID"], presetParam["propertyName"], property, e))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    const auto& json = ZGJson::convertToJson(property);
    QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
    return ZGWebModule::replyObject(doc.object());
}

ZGWebModule::Response ZGOPHandle::on_it_preset_ctrl(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    const auto& presetID = param.toString().toStdString();
    auto ctrlPrx = ZGProxyMng::instance()->getProxyOPPatrolDeviceCtrl();
    if (ctrlPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取设备控制服务代理对象失败"));
    try
    {
        ZG6000::ErrorInfo e;
        if (!ctrlPrx->presetPointCtrl(presetID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::on_image_capture(const QString& clientID,
                                                 const QVariantMap& headers,
                                                 const QJsonValue& param,
                                                 const QHttpServerRequest& req)
{
    const auto& yvID = param.toString().toStdString();
    auto ctrlPrx = ZGProxyMng::instance()->getProxyOPPatrolDeviceCtrl();
    if (ctrlPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取设备控制服务代理对象失败"));
    try
    {
        ZG6000::ErrorInfo e;
        std::string url;
        if (!ctrlPrx->captureImage(yvID, url, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(url.c_str());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::on_video_record(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"yvID", "duration"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    auto ctrlPrx = ZGProxyMng::instance()->getProxyOPPatrolDeviceCtrl();
    if (ctrlPrx == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取设备控制服务代理对象失败"));
    try
    {
        ZG6000::ErrorInfo e;
        const auto& yvID = object["yvID"].toString().toStdString();
        const auto& duration = object["duration"].toVariant().toInt();
        std::string url;
        if (!ctrlPrx->recordVideo(yvID, duration, url, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(url.c_str());
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::on_event_get(const QString& clientID,
                                             const QVariantMap& headers,
                                             const QJsonValue& param,
                                             const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"startID", "count"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& startID = object["startID"].toInteger();
    const auto& count = object["count"].toInt();
    std::string rtAppNodeID;
    QDateTime currTime = QDateTime::currentDateTime();
    QString tableName = QString("sp_his_event_%1").arg(currTime.date().year());
    QString sql = QString("SELECT * FROM %1 WHERE id >= '%2' AND subsystemID = 'ZG_SS_IOMS' AND eventTypeID = 'ZG_ET_SYSTEM' ORDER BY id DESC limit 0, %3")
                  .arg(tableName).arg(startID).arg(count);
    ZG6000::ListStringMap listRecord;
    auto dbProxy = ZGProxyMng::instance()->getProxySPDBDataHis();
    try
    {
        ZG6000::ErrorInfo e;
        if (!dbProxy->execQuerySqlToListMap(sql.toStdString(), listRecord, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonArray array;
        for (size_t i = listRecord.size() - 1; i != 0; --i)
        {
            auto& record = listRecord[i];
            record["color"] = m_mapAlarmLevelColor[record["alarmLevelID"]];
            QJsonObject eventObj;
            for (const auto& pair : record)
            {
                eventObj[pair.first.c_str()] = pair.second.c_str();
            }
            array.append(eventObj);
        }
        return ZGWebModule::replyObject(array);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::on_iu_task_download(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return iuCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskIUPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& taskIDArray = param.toArray();
        ZG6000::StringList listTaskID;
        for (auto taskRef : taskIDArray)
        {
            listTaskID.push_back(taskRef.toString().toStdString());
        }
        ZG6000::ListStringMap listTask, listItem;
        ZG6000::ErrorInfo e;
        if (!taskProxy->downloadTask(listTaskID, listTask, listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        QJsonObject object;
        QJsonArray taskArray, itemArray;
        for (const auto& task : listTask)
        {
            QJsonObject taskObject;
            for (const auto& [field, value] : task)
            {
                taskObject[field.c_str()] = value.c_str();
            }
            taskArray.push_back(taskObject);
        }
        for (const auto& item : listItem)
        {
            QJsonObject itemObject;
            for (const auto& [field, value] : item)
            {
                itemObject[field.c_str()] = value.c_str();
            }
            itemArray.push_back(itemObject);
        }
        object["task"] = taskArray;
        object["item"] = itemArray;
        return ZGWebModule::replyObject(object);
    });
}

ZGWebModule::Response ZGOPHandle::on_iu_task_temporary_create(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    return iuCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskIUPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"unlock", "params"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& unlockArray = object["unlock"].toArray();
        const auto& paramObject = object["params"].toObject();
        ZG6000::ListStringMap listUnlock;
        for (auto unlockRef : unlockArray)
        {
            const auto& unlockObject = unlockRef.toObject();
            if (!ZGWebModule::checkRequiredFields(unlockObject, {"templateID", "deviceID"}, errMsg))
                return ZGWebModule::errorObject(errMsg);
            ZG6000::StringMap unlock{
                {"templateID", unlockObject["templateID"].toString().toStdString()},
                {"deviceID", unlockObject["deviceID"].toString().toStdString()}
            };
            listUnlock.push_back(std::move(unlock));
        }
        ZG6000::StringMap params;
        for (auto it = paramObject.begin(); it != paramObject.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        std::string taskID;
        if (!taskProxy->createTemporaryTask(listUnlock, params, taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(taskID.c_str());
    });
}

ZGWebModule::Response ZGOPHandle::on_iu_task_template_create(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    return iuCall(clientID, param, req, [&](std::shared_ptr<ZG6000::ZGOPTaskIUPrx> taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"templateID", "appNodes", "params"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& templateID = object["templateID"].toString().toStdString();
        const auto& appNodeArray = object["appNodes"].toArray();
        const auto& paramObject = object["params"].toObject();
        ZG6000::StringList listAppNodeID;
        for (auto appNode : appNodeArray)
        {
            listAppNodeID.push_back(appNode.toString().toStdString());
        }
        ZG6000::StringMap params;
        for (auto it = paramObject.begin(); it != paramObject.end(); ++it)
        {
            params[it.key().toStdString()] = it.value().toString().toStdString();
        }
        ZG6000::ErrorInfo e;
        ZG6000::StringList listTaskID;
        if (!taskProxy->createTemplateTask(templateID, listAppNodeID, params, listTaskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listTaskID);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_iu_task_update(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return iuCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskIUPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskArray = param.toArray();
        ZG6000::ListStringMap listTask;
        for (auto taskRef : taskArray)
        {
            const auto& taskObj = taskRef.toObject();
            ZG6000::StringMap task;
            for (auto it = taskObj.begin(); it != taskObj.end(); ++it)
            {
                task[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listTask.push_back(std::move(task));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->updateTask(listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_iu_item_update(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return iuCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskIUPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& itemArray = param.toArray();
        ZG6000::ListStringMap listItem;
        for (auto itemRef : itemArray)
        {
            const auto& itemObj = itemRef.toObject();
            ZG6000::StringMap item;
            for (auto it = itemObj.begin(); it != itemObj.end(); ++it)
            {
                item[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
            }
            listItem.push_back(std::move(item));
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->updateItem(listItem, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_list(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        ZG6000::StringMap queryParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            queryParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ListStringMap listTask;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getWPTaskList(queryParam, listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listTask);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_user_list(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ListStringMap listUser;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getWPUser(taskID, listUser, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listUser);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_create(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"head", "user"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& headObj = object["head"].toObject();
        const auto& userArr = object["user"].toArray();
        const auto& taskHead = ZGWebModule::objectToStringMap(headObj);
        const auto& listUserID = ZGWebModule::arrayToStringList(userArr);
        ZG6000::ErrorInfo e;
        std::string taskID;
        if (!taskProxy->createWPTask(taskHead, listUserID, taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(taskID.c_str());
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_edit(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"id"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["id"].toString().toStdString();
        const auto& taskParam = ZGWebModule::objectToStringMap(object);
        ZG6000::ErrorInfo e;
        if (!taskProxy->editWPTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_user_edit(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "user"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& userArr = object["user"].toArray();
        const auto& listUserID = ZGWebModule::arrayToStringList(userArr);
        ZG6000::ErrorInfo e;
        if (!taskProxy->editWPUser(taskID, listUserID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_delete(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->deleteWPTask(taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_abolish(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->abolishWPTask(taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_confirm(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::StringMap taskParam;
        // const auto& taskParam = ZGWebModule::objectToStringMap(object);
        ZG6000::ErrorInfo e;
        if (!taskProxy->confirmTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_wp_task_back(const QString& clientID,
                                                const QVariantMap& headers,
                                                const QJsonValue& param,
                                                const QHttpServerRequest& req)
{
    return wpCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPWPManagerPrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::StringMap taskParam;
        ZG6000::ErrorInfo e;
        if (!taskProxy->backTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_device_get(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    const auto& appNodeID = param.toString().toStdString();
    QString sql = QString("SELECT a.deviceID, b.name AS deviceName, b.typeID, b.subtypeID, c.name AS appNodeName FROM mp_param_appnode_device a "
            "LEFT JOIN mp_param_device b ON a.deviceID = b.id "
            "LEFT JOIN sp_param_appnode c ON a.appNodeID = c.id "
            "WHERE a.appNodeID = '%1' AND typeID IN ('ZG_DT_DISCONNECTOR', 'ZG_DT_GROUND_SWITCH') ORDER BY a.id")
        .arg(appNodeID.c_str());
    ZG6000::ListStringMap listRecord1, listRecord2;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord1))
        return ZGWebModule::errorObject(QStringLiteral("获取应用节点关联设备失败"));
    sql = QString("SELECT a.id AS deviceID, a.name AS deviceName, a.typeID, a.subtypeID, b.name AS appNodeName FROM mp_param_device a "
            "LEFT JOIN sp_param_appnode b ON a.appNodeID = b.id "
            "WHERE appNodeID = '%1' AND typeID IN ('ZG_DT_DISCONNECTOR', 'ZG_DT_GROUND_SWITCH') ORDER BY a.id")
        .arg(appNodeID.c_str());
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRecord2))
        return ZGWebModule::errorObject(QStringLiteral("获取设备信息失败"));
    std::move(listRecord2.begin(), listRecord2.end(), std::back_inserter(listRecord1));
    ZG6000::StringList listDeviceID, listOverZoneDeviceID;
    for (auto& record : listRecord1)
    {
        listDeviceID.push_back(record["deviceID"]);
        if (record["subtypeID"] == "ZG_DS_DISCONNECTOR_OVERZONE")
            listOverZoneDeviceID.push_back(record["deviceID"]);
    }
    ZG6000::MapStringMap mapDeviceStatus;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::mgetPropertyValues(listDeviceID, {"Availbility"}, mapDeviceStatus, e))
    {
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    }
    ZG6000::MapStringMap mapDeviceNormalState;
    if (!ZGProxyCommon::mgetPropertyValues(listOverZoneDeviceID, {"NormalState"}, mapDeviceNormalState, e))
    {
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    }
    for (auto& record : listRecord1)
    {
        const auto& deviceID = record["deviceID"];
        auto it = mapDeviceStatus.find(deviceID);
        if (it != mapDeviceStatus.end())
        {
            record["Availbility"] = it->second["Availbility"];
        }
        it = mapDeviceNormalState.find(deviceID);
        if (it != mapDeviceNormalState.end())
        {
            record["NormalState"] = it->second["NormalState"];
        }
    }
    auto deviceArray = ZGWebModule::listStringMapToArray(listRecord1);
    return ZGWebModule::replyObject(deviceArray);
}

ZGWebModule::Response ZGOPHandle::on_outage_task_list(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap queryParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            queryParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ListStringMap listTask;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskList(queryParam, listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        const auto& json = ZGJson::convertToJson(listTask);
        QJsonDocument doc = QJsonDocument::fromJson(json.c_str());
        return ZGWebModule::replyObject(doc.array());
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_count(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"condition"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& condition = object["condition"].toString();
    QString sql = QString("SELECT COUNT(*) FROM op_param_task a WHERE %1").arg(condition);
    std::string taskCount;
    if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), taskCount))
        return ZGWebModule::errorObject(u8"获取任务数量失败");
    return ZGWebModule::replyObject(taskCount.c_str());
}

ZGWebModule::Response ZGOPHandle::on_outage_typical_list(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    const auto& object = param.toObject();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        ZG6000::StringMap queryParam;
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            queryParam[it.key().toStdString()] = it.value().toVariant().toString().toStdString();
        }
        ZG6000::ListStringMap listTask;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskList(queryParam, listTask, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        ZG6000::ListStringMap listTypicalTask;
        for (auto& task : listTask)
        {
            std::string typeID;
            ZGLOG_TRACE(QString("task id: %1").arg(task["id"].c_str()));
            if (!ZGProxyCommon::getDataByField("op_param_outage_task", task["id"], "outageTaskTypeID", typeID))
                continue;
            ZGLOG_TRACE(QString("typeID: %1, rtTaskStageID: %2").arg(typeID.c_str()).arg(task["rtTaskStageID"].c_str()));
            if (typeID == "ZG_OTT_TYPICAL" && task["rtTaskStageID"] == "ZG_TS_INIT")
                listTypicalTask.push_back(std::move(task));
        }
        const auto& taskArray = ZGWebModule::listStringMapToArray(listTypicalTask);
        return ZGWebModule::replyObject(taskArray);
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_typical_delete(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->deleteTypicalTask(taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_create(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();

        if (!ZGWebModule::checkRequiredFields(object, {"head", "devices", "users"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& headObj = object["head"].toObject();
        const auto& outageTypeID = headObj["outageTypeID"].toString().toStdString();
        const auto& deviceArray = object["devices"].toArray();
        const auto& userArray = object["users"].toArray();
        const auto& head = ZGWebModule::objectToStringMap(headObj);
        const auto& listDevice = ZGWebModule::arrayToListStringMap(deviceArray);
        const auto& listUser = ZGWebModule::arrayToListStringMap(userArray);
        ZG6000::ErrorInfo e;
        std::string taskID;
        if (!taskProxy->createTask(head, listDevice, listUser, taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject(taskID.c_str());
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_edit(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = token["userID"].toString().toStdString();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"head", "devices", "users"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& headObj = object["head"].toObject();
        const auto& taskID = headObj["id"].toString().toStdString();
        QString sql = QString("SELECT rtCreateUserID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
            return ZGWebModule::errorObject(QStringLiteral("获取任务创建用户失败"));
        if (listUserID.empty())
            return ZGWebModule::errorObject(QStringLiteral("任务不存在"));
        if (listUserID.front() != userID)
            return ZGWebModule::errorObject(QStringLiteral("非任务创建用户，无法编辑任务"));
        const auto& deviceArray = object["devices"].toArray();
        const auto& userArray = object["users"].toArray();
        const auto& head = ZGWebModule::objectToStringMap(headObj);
        const auto& listDevice = ZGWebModule::arrayToListStringMap(deviceArray);
        const auto& listUser = ZGWebModule::arrayToListStringMap(userArray);
        ZG6000::ErrorInfo e;
        if (!taskProxy->editTask(taskID, head, listDevice, listUser, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_delete(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = token["userID"].toString().toStdString();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        QString sql = QString("SELECT rtCreateUserID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
            return ZGWebModule::errorObject(QStringLiteral("获取任务创建用户失败"));
        if (listUserID.empty())
            return ZGWebModule::errorObject(QStringLiteral("任务不存在"));
        if (listUserID.front() != userID)
            return ZGWebModule::errorObject(QStringLiteral("非任务创建用户，无法删除任务"));
        ZG6000::ErrorInfo e;
        if (!taskProxy->deleteTask(taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_abolish(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        ZG6000::StringMap params;
        if (object.contains("params"))
        {
            const auto& paramObj = object["params"].toObject();
            params = ZGWebModule::objectToStringMap(paramObj);
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->abolishTask(taskID, params, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_cancel(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = token["userID"].toString().toStdString();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        QString sql = QString("SELECT rtCreateUserID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
            return ZGWebModule::errorObject(QStringLiteral("获取任务创建用户失败"));
        if (listUserID.empty())
            return ZGWebModule::errorObject(QStringLiteral("任务不存在"));
        if (listUserID.front() != userID)
            return ZGWebModule::errorObject(QStringLiteral("非任务创建用户，无法取消任务"));
        ZG6000::StringMap otp;
        if (object.contains("otp"))
        {
            const auto& otpObj = object["otp"].toObject();
            otp = ZGWebModule::objectToStringMap(otpObj);
        }
        QByteArray clientKey;
        if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
            return ZGWebModule::errorObject(errMsg);
        for (auto& [key, value]: otp)
        {
            QByteArray decryptedValue;
            if (!ZGWebModule::decryptWithClientKey(clientKey, value.c_str(), decryptedValue, errMsg))
                return ZGWebModule::errorObject(errMsg);
            otp[key] = decryptedValue.toStdString();
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->cancelTask(taskID, otp, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_info(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::StringMap head;
        ZG6000::ListStringMap listDevice, listUser;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskInfo(taskID, head, listDevice, listUser, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        auto headObj = ZGWebModule::stringMapToObject(head);
        auto deviceArr = ZGWebModule::listStringMapToArray(listDevice);
        auto userArr = ZGWebModule::listStringMapToArray(listUser);
        QJsonObject object;
        object["head"] = headObj;
        object["devices"] = deviceArr;
        object["users"] = userArr;
        return ZGWebModule::replyObject(object);
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_user(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ListStringMap listUser;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getTaskUsers(taskID, listUser, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        auto userArr = ZGWebModule::listStringMapToArray(listUser);
        return ZGWebModule::replyObject(userArr);
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_confirm(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::StringMap taskParam;
        ZG6000::ErrorInfo e;
        if (!taskProxy->confirmTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_start(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = token["userID"].toString().toStdString();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        QString sql = QString("SELECT rtCreateUserID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
            return ZGWebModule::errorObject(QStringLiteral("获取任务创建用户失败"));
        if (listUserID.empty())
            return ZGWebModule::errorObject(QStringLiteral("任务不存在"));
        if (listUserID.front() != userID)
            return ZGWebModule::errorObject(QStringLiteral("非任务创建用户，无法启动任务"));
        ZG6000::StringMap taskParam;
        ZG6000::ErrorInfo e;
        if (!taskProxy->startTask(taskID, taskParam, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_lock(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->lockTask(taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_unlock(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->unlockTask(taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_convert(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = token["userID"].toString().toStdString();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy) -> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        QString sql = QString("SELECT rtCreateUserID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
            return ZGWebModule::errorObject(QStringLiteral("获取任务创建用户失败"));
        if (listUserID.empty())
            return ZGWebModule::errorObject(QStringLiteral("任务不存在"));
        if (listUserID.front() != userID)
            return ZGWebModule::errorObject(QStringLiteral("非任务创建用户，无法转换任务"));
        ZG6000::ErrorInfo e;
        if (!taskProxy->convertTask(taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_task_move(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& userID = token["userID"].toString().toStdString();
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "oldUsers", "newUsers"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        QString sql = QString("SELECT rtCreateUserID FROM op_param_task WHERE id = '%1'").arg(taskID.c_str());
        ZG6000::StringList listUserID;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
            return ZGWebModule::errorObject(QStringLiteral("获取任务创建用户失败"));
        if (listUserID.empty())
            return ZGWebModule::errorObject(QStringLiteral("任务不存在"));
        if (listUserID.front() != userID)
            return ZGWebModule::errorObject(QStringLiteral("非任务创建用户，无法转移任务"));
        const auto& oldUsers = object["oldUsers"].toArray();
        const auto& newUsers = object["newUsers"].toArray();
        auto listOldUser = ZGWebModule::arrayToListStringMap(oldUsers);
        QByteArray clientKey;
        if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
            return ZGWebModule::errorObject(QString("get client key failed: %1").arg(errMsg));
        for (auto& oldUser : listOldUser)
        {
            auto rtOTP = oldUser["rtOTP"];
            QByteArray encryptedOTP = rtOTP.c_str();
            QByteArray originOTP;
            if (!ZGWebModule::decryptWithClientKey(clientKey, encryptedOTP, originOTP, errMsg))
                return ZGWebModule::errorObject(QString("decrypt otp failed: %1").arg(errMsg));
            oldUser["rtOTP"] = originOTP.data();
        }
        const auto& listNewUser = ZGWebModule::arrayToListStringMap(newUsers);
        ZG6000::ErrorInfo e;
        if (!taskProxy->moveTask(taskID, listOldUser, listNewUser, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_isolator_lock(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "deviceID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& OTP = object["OTP"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->lockIsolator(clientID.toStdString(), taskID, deviceID, OTP, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_isolator_lock_batch(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->lockIsolatorBatch(clientID.toStdString(), taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_isolator_unlock(const QString& clientID,
                                                          const QVariantMap& headers,
                                                          const QJsonValue& param,
                                                          const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "deviceID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& OTP = object["OTP"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->unlockIsolator(clientID.toStdString(), taskID, deviceID, OTP, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_isolator_unlock_batch(const QString& clientID,
                                                                const QVariantMap& headers,
                                                                const QJsonValue& param,
                                                                const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->unlockIsolatorBatch(clientID.toStdString(), taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_switch_lock(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "deviceID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& OTP = object["OTP"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->lockSwitch(clientID.toStdString(), taskID, deviceID, OTP, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_switch_lock_batch(const QString& clientID,
                                                            const QVariantMap& headers,
                                                            const QJsonValue& param,
                                                            const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->lockSwitchBatch(clientID.toStdString(), taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_switch_unlock_batch(const QString& clientID,
                                                              const QVariantMap& headers,
                                                              const QJsonValue& param,
                                                              const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->unlockSwitchBatch(clientID.toStdString(), taskID, {}, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_switch_unlock(const QString& clientID,
                                                        const QVariantMap& headers,
                                                        const QJsonValue& param,
                                                        const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "deviceID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& OTP = object["OTP"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->unlockSwitch(clientID.toStdString(), taskID, deviceID, OTP, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_switch_close(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "deviceID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& OTP = object["OTP"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->closeSwitch(clientID.toStdString(), taskID, deviceID, OTP, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_switch_open(const QString& clientID,
                                                      const QVariantMap& headers,
                                                      const QJsonValue& param,
                                                      const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "deviceID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& OTP = object["OTP"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->openSwitch(clientID.toStdString(), taskID, deviceID, OTP, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_otp_send(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "users"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& userArray = object["users"].toArray();
        ZG6000::StringList listUserID;
        for (auto userRef : userArray)
        {
            const auto& userID = userRef.toString().toStdString();
            listUserID.push_back(userID);
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->sendSMS(taskID, listUserID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_phone_change(const QString& clientID,
                                                       const QVariantMap& headers,
                                                       const QJsonValue& param,
                                                       const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "oldPhone", "newPhone"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& oldPhone = object["oldPhone"].toString().toStdString();
        const auto& newPhone = object["newPhone"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->changePhone(taskID, oldPhone, newPhone, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_ptw_apply(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->applyPTW(taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_sft_apply(const QString& clientID,
                                                    const QVariantMap& headers,
                                                    const QJsonValue& param,
                                                    const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->applySFT(taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_ptw_cancel(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& otpObject = object["OTP"].toObject();
        ZG6000::StringMap otpMap;
        QByteArray clientKey;
        if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
            return ZGWebModule::errorObject(QString("get client key failed: %1").arg(errMsg));
        for (auto it = otpObject.begin(); it != otpObject.end(); ++it)
        {
            const auto& encryptedOTP = it.value().toString();
            if (!encryptedOTP.isEmpty())
            {
                QByteArray baEncryptedOTP = encryptedOTP.toLatin1();
                QByteArray originOTP;
                if (!ZGWebModule::decryptWithClientKey(clientKey, baEncryptedOTP, originOTP, errMsg))
                    return ZGWebModule::errorObject(QString("decrypt OTP failed: %1").arg(errMsg));
                otpMap[it.key().toStdString()] = originOTP.data();
            }
            else
                otpMap[it.key().toStdString()] = "";
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->cancelPTW(taskID, otpMap, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_sft_cancel(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& otpObject = object["OTP"].toObject();
        ZG6000::StringMap otpMap;
        QByteArray clientKey;
        if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
            return ZGWebModule::errorObject(QString("get client key failed: %1").arg(errMsg));
        for (auto it = otpObject.begin(); it != otpObject.end(); ++it)
        {
            const auto& encryptedOTP = it.value().toString();
            if (!encryptedOTP.isEmpty())
            {
                QByteArray baEncryptedOTP = encryptedOTP.toLatin1();
                QByteArray originOTP;
                if (!ZGWebModule::decryptWithClientKey(clientKey, baEncryptedOTP, originOTP, errMsg))
                    return ZGWebModule::errorObject(QString("decrypt OTP failed: %1").arg(errMsg));
                otpMap[it.key().toStdString()] = originOTP.data();
            }
            else
                otpMap[it.key().toStdString()] = "";
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->cancelSFT(taskID, otpMap, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_otp_save(const QString& clientID,
                                                   const QVariantMap& headers,
                                                   const QJsonValue& param,
                                                   const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy) -> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "OTP"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& otp = object["OTP"].toObject();
        ZG6000::StringMap otpMap;
        QByteArray clientKey;
        if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
            return ZGWebModule::errorObject(QString("get client key failed: %1").arg(errMsg));
        for (auto it = otp.begin(); it != otp.end(); ++it)
        {
            const auto& encryptedOTP = it.value().toString();
            if (!encryptedOTP.isEmpty())
            {
                QByteArray baEncryptedOTP = encryptedOTP.toLatin1();
                QByteArray originOTP;
                if (!ZGWebModule::decryptWithClientKey(clientKey, baEncryptedOTP, originOTP, errMsg))
                    return ZGWebModule::errorObject(QString("decrypt OTP failed: %1").arg(errMsg));
                otpMap[it.key().toStdString()] = originOTP.data();
            }
            else
                otpMap[it.key().toStdString()] = "";
        }
        ZG6000::ErrorInfo e;
        if (!taskProxy->saveOTP(taskID, otpMap, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_device_monitor(const QString& clientID,
                                                         const QVariantMap& headers,
                                                         const QJsonValue& param,
                                                         const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        ZG6000::ListStringMap listDevice;
        if (!taskProxy->getMonitorDevices(taskID, listDevice, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        auto deviceArray = ZGWebModule::listStringMapToArray(listDevice);
        return ZGWebModule::replyObject(deviceArray);
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_confirm(const QString& clientID,
                                                  const QVariantMap& headers,
                                                  const QJsonValue& param,
                                                  const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& taskID = param.toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->confirmOutage(taskID, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_event_save(const QString& clientID,
                                                     const QVariantMap& headers,
                                                     const QJsonValue& param,
                                                     const QHttpServerRequest& req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "event"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& event = object["event"].toString().toStdString();
        std::string deviceID;
        if (object.contains("deviceID"))
            deviceID = object["deviceID"].toString().toStdString();
        ZG6000::ErrorInfo e;
        if (!taskProxy->saveEvent(taskID, deviceID, event, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        return ZGWebModule::replyObject("");
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_group_device_get(const QString& clientID,
                                                           const QVariantMap& headers,
                                                           const QJsonValue& param,
                                                           const QHttpServerRequest& req)
{
    const auto& deviceID = param.toString();
    QString sql = QString("SELECT a.dstDeviceID as deviceID, b.name AS deviceName, b.typeID, b.subtypeID, "
                          "b.appNodeID, c.name AS appNodeName FROM mp_param_device_relation a "
                          "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
                          "LEFT JOIN sp_param_appnode c ON b.appNodeID = c.id "
                          "WHERE a.srcDeviceID = '%1' AND a.relationTypeID = 'ZG_RT_ASSOC' "
                          "ORDER BY a.dstDeviceID").arg(deviceID);
    ZG6000::ListStringMap listDevice;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listDevice))
        return ZGWebModule::errorObject(QStringLiteral("获取设备失败"));
    for (auto& device : listDevice)
    {
        const auto& groupDeviceID = device["deviceID"];
        const auto& subtypeID = device["subtypeID"];
        std::string availbility;
        ZG6000::ErrorInfo e;
        if (!ZGProxyCommon::getPropertyValue(groupDeviceID, "Availbility", availbility, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        device["availbility"] = availbility;
        if (subtypeID == "ZG_DS_DISCONNECTOR_OVERZONE")
        {
            std::string normalState;
            if (!ZGProxyCommon::getPropertyValue(groupDeviceID, "NormalState", normalState, e))
                return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
            device["normalState"] = normalState;
        }
    }
    auto deviceArray = ZGWebModule::listStringMapToArray(listDevice);
    return ZGWebModule::replyObject(deviceArray);
}

ZGWebModule::Response ZGOPHandle::on_outage_lock_device_password_set(const QString& clientID,
                                                                   const QVariantMap& headers,
                                                                   const QJsonValue& param,
                                                                   const QHttpServerRequest& req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_CTRL"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& array = param.toArray();
    ZG6000::StringList listSql;
    for (const auto& ref : array)
    {
        const auto& object = ref.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "password"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& password = object["password"].toString().toStdString();
        if (deviceID == "*")
        {
            QString sql = QString("UPDATE mp_param_device SET password = '%1' WHERE typeID = 'ZG_DT_SAFE_LOCK'").arg(password.c_str());
            listSql.push_back(sql.toStdString());
            break;
        }
        else
        {
            QString sql = QString("UPDATE mp_param_device SET password = '%1' WHERE id = '%2' AND typeID = 'ZG_DT_SAFE_LOCK'").arg(password.c_str(), deviceID.c_str());
            listSql.push_back(sql.toStdString());
        }
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
            return ZGWebModule::errorObject(QStringLiteral("设置密码失败"));
    }
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGOPHandle::on_outage_isolator_normalstate_set(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_CTRL"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& array = param.toArray();
    ZG6000::MapStringMap devicesNormalState;
    for (const auto& ref : array)
    {
        const auto& object = ref.toObject();
        if (!ZGWebModule::checkRequiredFields(object, {"deviceID", "normalState"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& deviceID = object["deviceID"].toString().toStdString();
        const auto& normalState = object["normalState"].toString().toStdString();
        devicesNormalState[deviceID]["NormalState"] = normalState;
    }
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::mupdatePropertyValues(devicesNormalState, e, true))
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    return ZGWebModule::replyObject("");
}

ZGWebModule::Response ZGOPHandle::on_outage_lock_external_lock_enable(const QString& clientID,
                                                                    const QVariantMap& headers,
                                                                    const QJsonValue& param,
                                                                    const QHttpServerRequest& req)
{
    const auto& taskID = param.toString().toStdString();
    ZG6000::StringMap externalLockEnable;
    const auto& object = ZGWebModule::stringMapToObject(externalLockEnable);
    return ZGWebModule::replyObject(object);
}

ZGWebModule::Response ZGOPHandle::on_outage_unlock_external_lock(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = object["deviceID"].toString().toStdString();
    return sendCtrlCommand(clientID.toStdString(), deviceID, "CMD_OutInterlockOff", "2");
}

ZGWebModule::Response ZGOPHandle::on_outage_lock_external_lock(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    QString errMsg;
    QJsonObject token;
    if (!ZGWebModule::verifyAuthentication(clientID, headers, {"ZG_HP_OUTAGE_TASK"}, token, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& object = param.toObject();
    if (!ZGWebModule::checkRequiredFields(object, {"deviceID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& deviceID = object["deviceID"].toString().toStdString();
    return sendCtrlCommand(clientID.toStdString(), deviceID, "CMD_OutInterlockOn", "2");
}

ZGWebModule::Response ZGOPHandle::on_outage_close_condition_get(const QString &clientID, const QVariantMap& headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    return outageCall(clientID, param, req, [&](const std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>& taskProxy)-> ZGWebModule::Response
    {
        const auto& object = param.toObject();
        QString errMsg;
        if (!ZGWebModule::checkRequiredFields(object, {"taskID", "deviceID"}, errMsg))
            return ZGWebModule::errorObject(errMsg);
        const auto& taskID = object["taskID"].toString().toStdString();
        const auto& deviceID = object["deviceID"].toString().toStdString();
        ZG6000::ListStringMap conditions;
        ZG6000::ErrorInfo e;
        if (!taskProxy->getSwitchCloseConditions(taskID, deviceID, conditions, e))
            return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
        auto conditionArray = ZGWebModule::listStringMapToArray(conditions);
        return ZGWebModule::replyObject(conditionArray);
    });
}

ZGWebModule::Response ZGOPHandle::on_outage_otp_get(const QString &clientID, const QVariantMap &headers, const QJsonValue &param, const QHttpServerRequest &req)
{
    const auto& object = param.toObject();
    QString errMsg;
    if (!ZGWebModule::checkRequiredFields(object, {"taskID"}, errMsg))
        return ZGWebModule::errorObject(errMsg);
    const auto& taskID = object["taskID"].toString().toStdString();
    ZG6000::StringList listUserID;
    if (object.contains("users"))
    {
        const auto& userArray = object["users"].toArray();
        for (auto userRef : userArray)
        {
            const auto& userID = userRef.toString().toStdString();
            listUserID.push_back(userID);
        }
    }
    QString sql;
    if (listUserID.empty())
        sql = QString("SELECT userID, rtOTP, rtSavedOTP FROM op_param_outage_task_user WHERE taskID = '%1'")
                  .arg(taskID.c_str());
    else
    {
        const auto& users = ZGUtils::join(listUserID, ",", "'", "'");
        sql = QString("SELECT userID, rtOTP, rtSavedOTP FROM op_param_outage_task_user WHERE taskID = '%1' AND userID IN (%2)")
                  .arg(taskID.c_str(), users.c_str());
    }
    ZG6000::ListStringMap listOTP;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listOTP))
        return ZGWebModule::errorObject(QStringLiteral("获取任务用户OTP失败"));
    // 取出rtOTP和rtSavedOTP，使用服务器对称密钥解密，再使用客户端密钥加密
    QByteArray clientKey;
    if (!ZGWebModule::getClientKey(clientID, clientKey, errMsg))
        return ZGWebModule::errorObject(QString("get client key failed: %1").arg(errMsg));
    for (auto& otp : listOTP)
    {
        const auto& rtOTP = otp["rtOTP"];
        const auto& rtSavedOTP = otp["rtSavedOTP"];
        if (!rtOTP.empty())
        {
            QByteArray baEncryptedOTP = QByteArray::fromBase64(rtOTP.c_str());
            QByteArray aesKey = ZGWebModule::getServerAESKey();
            QByteArray originOTP;
            std::string errorString;
            if (!ZGSecure::aesDecrypt(baEncryptedOTP, originOTP, ZGWebModule::getServerAESKey(), errorString, ZGSecure::EncryptMode::ecb))
                return ZGWebModule::errorObject(QString("decrypt OTP failed: %1").arg(errMsg));
            QByteArray baClientEncryptedOTP;
            if (!ZGWebModule::encryptWithClientKey(clientKey, originOTP, baClientEncryptedOTP, errMsg))
                return ZGWebModule::errorObject(QString("encrypt OTP failed: %1").arg(errMsg));
            otp["rtOTP"] = baClientEncryptedOTP.data();
        }
        if (!rtSavedOTP.empty())
        {
            QByteArray baEncryptedSavedOTP = QByteArray::fromBase64(rtSavedOTP.c_str());
            QByteArray originSavedOTP;
            std::string errorString;
            if (!ZGSecure::aesDecrypt(baEncryptedSavedOTP, originSavedOTP, ZGWebModule::getServerAESKey(), errorString, ZGSecure::EncryptMode::ecb))
                return ZGWebModule::errorObject(QString("decrypt OTP failed: %1").arg(errMsg));
            QByteArray baClientEncryptedSavedOTP;
            if (!ZGWebModule::encryptWithClientKey(clientKey, originSavedOTP, baClientEncryptedSavedOTP, errMsg))
                return ZGWebModule::errorObject(QString("encrypt OTP failed: %1").arg(errMsg));
            otp["rtSavedOTP"] = baClientEncryptedSavedOTP.data();
        }
    }
    const auto& otpArray = ZGWebModule::listStringMapToArray(listOTP);
    return ZGWebModule::replyObject(otpArray);
}

ZGWebModule::Response ZGOPHandle::otCall(const QString& clientID,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req,
                                       const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPTaskOTPrx>)>& func)
{
    auto otProxy = ZGProxyMng::instance()->getProxyOPTaskOT();
    if (otProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取操作票服务代理对象失败"));
    try
    {
        return func(otProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::itCall(const QString& clientID,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req,
                                       const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPTaskITPrx>)>& func)
{
    auto itProxy = ZGProxyMng::instance()->getProxyOPTaskIT();
    if (itProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取巡检服务代理对象失败"));
    try
    {
        return func(itProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::iuCall(const QString& clientID,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req,
                                       const std::function<ZGWebModule::Response (std::shared_ptr<ZG6000::ZGOPTaskIUPrx>)>& func)
{
    auto iuProxy = ZGProxyMng::instance()->getProxyOPTaskIU();
    if (iuProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取巡检服务代理对象失败"));
    try
    {
        return func(iuProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::wpCall(const QString& clientID,
                                       const QJsonValue& param,
                                       const QHttpServerRequest& req,
                                       const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPWPManagerPrx>)>& func)
{
    auto wpProxy = ZGProxyMng::instance()->getProxyOPWPManager();
    if (wpProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取请销点管理服务代理对象失败"));
    try
    {
        return func(wpProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::outageCall(const QString& clientID,
                                           const QJsonValue& param,
                                           const QHttpServerRequest& req,
                                           const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>)>& func)
{
    auto outageProxy = ZGProxyMng::instance()->getProxyOPTaskOutage();
    if (outageProxy == nullptr)
        return ZGWebModule::errorObject(QStringLiteral("获取断电任务服务代理对象失败"));
    try
    {
        return func(outageProxy);
    }
    catch (const Ice::Exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::convertRuleItem(const ZG6000::ListStringMap& listRecord)
{
    try
    {
        QJsonArray array;
        for (const auto& record : listRecord)
        {
            QJsonObject ruleObj;
            const auto& id = ZGUtils::get(record, "id");
            const auto& dataID = ZGUtils::get(record, "dataID");
            ruleObj["dataID"] = dataID.c_str();
            const auto& operatorID = ZGUtils::get(record, "operatorID");
            ruleObj["operatorID"] = operatorID.c_str();
            const auto& expectValue = ZGUtils::get(record, "value");
            ruleObj["expectValue"] = expectValue.c_str();
            const auto& actualValue = ZGUtils::get(record, "rtNewValue");
            ruleObj["actualValue"] = actualValue.c_str();
            const auto& result = ZGUtils::get(record, "result");
            ruleObj["id"] = id.c_str();
            auto pair = m_mapOperator.find(operatorID);
            if (pair != m_mapOperator.end())
                ruleObj["operator"] = pair->second.c_str();
            else
                ruleObj["operator"] = u8"未知";
            const auto& name = ZGUtils::get(record, "name");
            ruleObj["dataName"] = name.c_str();
            const auto& dataCategoryID = ZGUtils::get(record, "dataCategoryID");
            std::string expectProp = dataCategoryID + "/" + expectValue;
            std::string actualProp = dataCategoryID + "/" + actualValue;
            pair = m_mapDataCategoryProperty.find(expectProp);
            if (pair != m_mapDataCategoryProperty.end())
                ruleObj["expectValueDesc"] = pair->second.c_str();
            else
                ruleObj["expectValueDesc"] = expectValue.c_str();
            pair = m_mapDataCategoryProperty.find(actualProp);
            if (pair != m_mapDataCategoryProperty.end())
                ruleObj["actualValueDesc"] = pair->second.c_str();
            else
                ruleObj["actualValueDesc"] = actualValue.c_str();
            ruleObj["result"] = result.c_str();
            array.append(ruleObj);
        }
        return ZGWebModule::replyObject(array);
    }
    catch (const std::exception& e)
    {
        return ZGWebModule::errorObject(e.what());
    }
}

ZGWebModule::Response ZGOPHandle::sendCtrlCommand(const std::string &clientID, const std::string &deviceID, const std::string &propertyName, const std::string &value)
{
    ZGRedisClient* client = ZGRuntime::instance()->getRedisClientRTQueue();
    if (client == nullptr)
        return ZGWebModule::errorObject("getRedisClientRTQueue error.");
    std::string tableName, dataID;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getDataIDByProperty(deviceID, propertyName, tableName, dataID, e))
    {
        return ZGWebModule::errorObject(ZGJson::convertToJson(e).c_str());
    }
    QJsonObject command;
    command["id"] = dataID.c_str();
    QString commandID;
    if (tableName == "mp_param_dataset_yk")
    {
        commandID = "ZG_DC_YK_EXEC";
    }
    if (tableName == "mp_param_dataset_ys")
    {
        commandID = "ZG_DC_YS_EXEC";
    }
    command["commandID"] = commandID;
    command["isReturnValue"] = "0";
    command["srcType"] = "auto";
    command["srcID"] = clientID.c_str();
    command["rtCode"] = QString::number(ZGUtils::genNumber(0, 10000));
    command["rtValue"] = value.c_str();
    command["rtCommandTime"] = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
    QJsonArray array;
    array.append(command);
    QJsonDocument doc(array);
    long long size;
    QString errMsg;
    std::string topicName;
    if (tableName == "mp_param_dataset_yk")
    {
        topicName = "ZG_Q_SYSTEM_YK";
    }
    if (tableName == "mp_param_dataset_ys")
    {
        topicName = "ZG_Q_SYSTEM_YS";
    }
    if (!client->rpush(topicName.c_str(), doc.toJson(), size, errMsg))
    {
        return ZGWebModule::errorObject(errMsg);
    }
    return ZGWebModule::replyObject("");
}



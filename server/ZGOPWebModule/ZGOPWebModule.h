#ifndef ZGOPWEBMODULE_H
#define ZGOPWEBMODULE_H

#include "ZGWebModule.h"

class ZGOPHandle;
class ZGOPWebModule : public ZGWebModule
{
    Q_OBJECT
    Q_PLUGIN_METADATA(IID ZGWebModule_iid FILE "ZGOPWebModule.json")
    Q_INTERFACES(ZGWebModule)

public:
    explicit ZGOPWebModule(QObject *parent = nullptr);

    // ZGWebModule interface
public:
    bool initialize() override;
    QString prefix() override;

private:
    ZGOPHandle* m_pHandle{nullptr};
};

#endif // ZGOPWEBMODULE_H

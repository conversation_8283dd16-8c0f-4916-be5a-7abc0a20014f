#ifndef ZGOPHANDLE_H
#define ZGOPHANDLE_H

#include <unordered_set>
#include "ZGWebModule.h"
#include "ZGProxyCommon.h"
#include "ZGOPTaskOT.h"
#include "ZGOPTaskIT.h"
#include "ZGOPTaskIU.h"

class ZGOPHandle : public QObject
{
    Q_OBJECT
public:
    explicit ZGOPHandle(QObject *parent = nullptr);
    bool initialize();

public:
    ZGWebModule::Response on_task_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_item_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_item_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_typical_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_template_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_count(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_edit(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_info(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_start(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_pause(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_resume(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_retry(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_item_skip(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_abolish(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_confirm(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_convert(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_download(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_task_transfer(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_item_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_preview_start(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_preview_stop(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_preview_pause(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_preview_resume(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_preview_retry(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_preview_confirm(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_device_term_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_common_term_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_ot_term_rule_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_typical_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_count(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_info(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_item_info(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_download(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_tasktype_object_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_typical_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_special_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_uav_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_edit(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_start(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_pause(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_resume(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_retry(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_abolish(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_confirm(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_convert(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_task_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_item_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_action_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_action_yv_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_it_preset_ctrl(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_image_capture(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_video_record(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_event_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_iu_task_download(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_iu_task_temporary_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_iu_task_template_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_iu_task_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_iu_item_update(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_user_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_edit(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_user_edit(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_abolish(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_confirm(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_wp_task_back(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_device_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_count(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_typical_list(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_typical_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_create(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_edit(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_delete(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_abolish(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_cancel(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_info(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_user(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_confirm(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_start(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_lock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_unlock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_convert(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_task_move(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_isolator_lock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_isolator_lock_batch(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_isolator_unlock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_isolator_unlock_batch(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_switch_lock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_switch_lock_batch(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_switch_unlock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_switch_unlock_batch(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_switch_close(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_switch_open(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_otp_send(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_phone_change(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_ptw_apply(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_sft_apply(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_ptw_cancel(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_sft_cancel(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_otp_save(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_device_monitor(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_confirm(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_event_save(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_group_device_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_lock_device_password_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_isolator_normalstate_set(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_lock_external_lock_enable(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_unlock_external_lock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_lock_external_lock(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_close_condition_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);
    ZGWebModule::Response on_outage_otp_get(const QString& clientID, const QVariantMap& headers, const QJsonValue& param, const QHttpServerRequest& req);

private:
    ZGWebModule::Response otCall(const QString& clientID, const QJsonValue& param,
        const QHttpServerRequest& req, const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPTaskOTPrx>)>& func);
    ZGWebModule::Response itCall(const QString& clientID, const QJsonValue& param,
        const QHttpServerRequest& req, const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPTaskITPrx>)>& func);
    ZGWebModule::Response iuCall(const QString& clientID, const QJsonValue& param,
        const QHttpServerRequest& req, const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPTaskIUPrx>)>& func);
    ZGWebModule::Response wpCall(const QString& clientID, const QJsonValue& param,
        const QHttpServerRequest& req, const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPWPManagerPrx>)>& func);
    ZGWebModule::Response outageCall(const QString& clientID, const QJsonValue& param,
        const QHttpServerRequest& req, const std::function<ZGWebModule::Response(std::shared_ptr<ZG6000::ZGOPTaskOutagePrx>)>& func);
    ZGWebModule::Response convertRuleItem(const ZG6000::ListStringMap& listRecord);
    ZGWebModule::Response sendCtrlCommand(const std::string& clientID,
                         const std::string& deviceID,
                         const std::string& propertyName,
                         const std::string& value);


private:
    std::unordered_map<std::string, std::string> m_mapAlarmLevelColor;
    ZG6000::StringMap m_mapOperator;
    ZG6000::StringMap m_mapDataCategory;
    ZG6000::StringMap m_mapDataCategoryProperty;
    std::unordered_set<std::string> m_setTaskField{ "id", "name", "taskTypeID", "appNodeID", "subsystemID", "majorID",
                                                    "rtOperUserID", "rtMonUserID", "rtStartTime", "rtEndTime", "rtTaskStateID"};
    std::unordered_set<std::string> m_setITTaskField{ "id", "typeID", "rtCurrentItemID"};
};

#endif // ZGOPHANDLE_H

#include "ZGEXNBWebClientMng.h"

#include <QCoreApplication>
#include <QJsonDocument>
#include <QThread>
#include <QDateTime>

#include "ZGDebugMng.h"
#include "ZGJson.h"
#include "ZGProxyCommon.h"
#include "ZGMqttClient.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

ZGEXNBWebClientMng* g_pInstance = nullptr;

ZGEXNBWebClientMng* ZGEXNBWebClientMng::instance()
{
    if (g_pInstance == nullptr)
        g_pInstance = new ZGEXNBWebClientMng;
    return g_pInstance;
}

void ZGEXNBWebClientMng::init()
{
    initEvents();
    while (!initServerInstConfig())
    {
        ZGLOG_ERROR("initServerInstConfig error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initParams())
    {
        ZGLOG_ERROR("initParams error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initMqttClient())
    {
        ZGLOG_ERROR("initMqttClient error.");
        QThread::msleep(m_initInterval * 1000);
    }
    while (!initPublishPoints())
    {
        ZGLOG_ERROR("initPublishPoints error.");
        QThread::msleep(m_initInterval * 1000);
    }
    requestAuthorization();
    m_initialized = true;
    m_timer.start(1000);
    ZGLOG_INFO("ZGEXNBWebClient init finished.");
}

void ZGEXNBWebClientMng::onReceivedData(int requestId, QByteArray data, int statusCode)
{
    if (statusCode != 200)
    {
        ZGLOG_ERROR("statusCode error: " + QString::number(statusCode));
        return;
    }
    switch (static_cast<RequestCode>(requestId))
    {
    case rcAuthorization:
        processAuthorization(data);
        requestUsers();
        publishDeviceStates();
        break;
    case rcUsers:
        processUsers(data);
        break;
    case rcTaskState:
    case rcDeviceStates:
    case rcWarnStates:
        break;
    case rcHeartbeat:
        processHeartbeat(data);
        break;
    }
}

ZGEXNBWebClientMng::ZGEXNBWebClientMng(QObject* parent)
    : QObject{parent}
{
    m_pHttpClient = new ZGHttpClient(this);
}

void ZGEXNBWebClientMng::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord)
{
    if (!m_initialized)
        return;
    if (reason != "change")
        return;
    if (oper == "update")
    {
        try
        {
            for (const auto& record : listRecord)
            {
                if (tableName == "op_param_task")
                {
                    const auto& id = ZGUtils::get(record, "id").newValue;
                    // 查找record中是否存在rtTaskStateID字段
                    auto iter = record.find("rtTaskStateID");
                    if (iter == record.end())
                        continue;
                    const std::string& rtTaskStateID = iter->second.newValue;
                    // 从实时表中获取任务阶段rtTaskStageID
                    std::string rtTaskStageID;
                    if (!ZGProxyCommon::getDataByField("op_param_task", id, "rtTaskStageID", rtTaskStageID))
                    {
                        ZGLOG_ERROR(QString("Can't get rtTaskStageID from op_param_task, taskID = %1").arg(id.c_str()));
                        continue;
                    }
                    std::string rtNumber;
                    if (!ZGProxyCommon::getDataByField("op_param_ot", id, "rtNumber", rtNumber))
                    {
                        ZGLOG_ERROR(QString("Can't get rtNumber from op_param_ot, taskID = %1").arg(id.c_str()));
                        continue;
                    }
                    QMap<QString, QString> taskInfo;
                    taskInfo["work_code"] = rtNumber.c_str();
                    taskInfo["rtTaskStateID"] = rtTaskStateID.c_str();
                    const auto& json = ZGJson::convertToJson(taskInfo);
                    QString url = QString("http://%1/api/v1/safety_interlock/power_outage/status/update").arg(m_hostAddress);
                    postJson(rcTaskState, url, json.toUtf8());
                }
                if (tableName == "mp_param_dataset_yx" || (tableName == "mp_param_dataset_yc"))
                {
                    const auto& id = ZGUtils::get(record, "id").newValue;
                    auto iter = record.find("rtNewValue");
                    if (iter == record.end())
                        continue;
                    const auto& rtNewValue = iter->second.newValue;
                    auto it = m_mapDataProperty.find(id);
                    if (it != m_mapDataProperty.end())
                        processDataProperty(rtNewValue, it->second);
                    it = m_mapWarnDataProperty.find(id);
                    if (it != m_mapWarnDataProperty.end())
                        processWarnDataProperty(id, rtNewValue, it->second);
                }
            }
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
}

void ZGEXNBWebClientMng::onTimer()
{
    // 每隔5分钟执行重新授权、请求用户、发布状态
    static int count = 0;
    count++;
    if (count == 300)
    {
        count = 0;
        requestAuthorization();
    }
    // 每隔10秒发送心跳
    static int heartbeatCount = 0;
    heartbeatCount++;
    if (heartbeatCount == 10)
    {
        heartbeatCount = 0;
        sendHeartbeat();
    }
}

void ZGEXNBWebClientMng::initEvents()
{
    connect(&m_timer, &QTimer::timeout, this, &ZGEXNBWebClientMng::onTimer);
    connect(m_pHttpClient, &ZGHttpClient::receivedData, this, &ZGEXNBWebClientMng::onReceivedData);
}

bool ZGEXNBWebClientMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    auto pair = serverGroup.find("host");
    if (pair == serverGroup.end())
    {
        ZGLOG_WARN("host not found in server config");
        return false;
    }
    m_hostAddress = pair.value();
    pair = serverGroup.find("portMapID");
    if (pair == serverGroup.end())
    {
        ZGLOG_WARN("portMapID not found in server config");
        return false;
    }
    m_portMapID = pair.value();
    pair = serverGroup.find("portWarnMapID");
    if (pair == serverGroup.end())
    {
        ZGLOG_WARN("portWarnMapID not found in server config");
        return false;
    }
    m_portWarnMapID = pair.value();
    pair = serverGroup.find("clientID");
    if (pair == serverGroup.end())
    {
        ZGLOG_WARN("clientID not found in server config");
        return false;
    }
    m_clientID = pair.value();
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
    return true;
}

bool ZGEXNBWebClientMng::initServerInstInfo()
{
    m_mapDeviceType = ZG6000::StringMap{
        {"ZG_DT_GROUND_SWITCH", "grounding_device"},
        {"ZG_DT_DISCONNECTOR", "disconnector"},
        {"ZG_DT_ACCESS_CONTROL", "access_device"},
        {"ZG_DT_PLATFORM", "maintenance_platform"}
    };
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    m_localNodeID = ZGPubFun::getLocalNodeID().toStdString();
    return true;
}

bool ZGEXNBWebClientMng::initParams()
{
    QString sql = QString("SELECT id FROM sp_param_appnode");
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), m_listAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("获取应用节点ID失败"));
        return false;
    }
    sql = QString("SELECT id, typeID, appNodeID FROM mp_param_device");
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapDeviceParam))
    {
        ZGLOG_ERROR(QStringLiteral("获取设备信息失败"));
        return false;
    }
    sql = QString("SELECT deviceID AS id, id AS regionID FROM mp_param_region");
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapDeviceRegion))
    {
        ZGLOG_ERROR(QString("获取设备区域信息失败"));
        return false;
    }
    return true;
}

bool ZGEXNBWebClientMng::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_pMqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_pMqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage error.");
        return false;
    }
    m_pMqttClient->connectToHost();
    return true;
}

bool ZGEXNBWebClientMng::initPublishPoints()
{
    QString sql = QString("SELECT dataID AS id, domain, channelAddr FROM mp_param_port_map_yx WHERE mapID = '%1'").arg(m_portMapID);
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapDataProperty))
    {
        ZGLOG_ERROR(QStringLiteral("获取映射表数据出错"));
        return false;
    }
    sql = QString("SELECT dataID AS id, domain, channelAddr FROM mp_param_port_map_yx WHERE mapID = '%1'").arg(m_portWarnMapID);
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), m_mapWarnDataProperty))
    {
        ZGLOG_ERROR(QStringLiteral("获取告警映射表数据出错"));
        return false;
    }
    return true;
}

void ZGEXNBWebClientMng::requestAuthorization()
{
    QString url = QString("http://%1/api/v1/authentication/oauth2/token").arg(m_hostAddress);
    ZGLOG_TRACE(url);
    const QString& clientKey = "m9VK9fScy4pleUPXIJIINxW0yDfTtZWUrDPGgLZb";
    const QString& clientSecret = "ZPeCdzLVAHh3Wvu67Yq4JyklDqjs78l9HE9cpes8";
    const QString& grantType = "client_credentials";
    QList<QPair<QByteArray, QByteArray>> listHeader;
    const QString& authKey = QString("%1:%2").arg(clientKey).arg(clientSecret).toLatin1().toBase64();
    const QString& authValue = QString("Basic %1").arg(authKey);
    listHeader.append(qMakePair(QByteArray("Authorization"), authValue.toLatin1()));
    const QString& contentType = "application/x-www-form-urlencoded";
    listHeader.append(qMakePair(QByteArray("Content-Type"), contentType.toLatin1()));
    const QString& body = QString("grant_type=%1").arg(grantType);
    m_pHttpClient->postJson(rcAuthorization, url, listHeader, body.toLatin1());
}

void ZGEXNBWebClientMng::requestUsers()
{
    QString url = QString("http://%1/api/v1/safety_interlock/employee/list").arg(m_hostAddress);
    ZGLOG_TRACE(url);
    postJson(rcUsers, url, {});
}

void ZGEXNBWebClientMng::publishDeviceStates()
{
    ZG6000::StringList listDataID, listDataValue;
    for (const auto& pair : m_mapDataProperty)
    {
        listDataID.push_back(pair.first);
    }
    // 获取所有数据点的值
    if (!ZGProxyCommon::mgetDataByField("mp_param_dataset_yx", listDataID, "rtNewValue", listDataValue))
    {
        ZGLOG_ERROR(QStringLiteral("获取数据点值失败"));
        return;
    }
    std::map<std::string, ZG6000::MapStringMap> appNodeDeviceProperties;
    try
    {
        for (size_t i = 0; i < listDataID.size(); ++i)
        {
            const auto& dataID = listDataID[i];
            const auto& dataValue = listDataValue[i];
            const auto& property = m_mapDataProperty[dataID];
            const auto& deviceID = ZGUtils::get(property, "domain");
            const auto& appNodeID = ZGUtils::get(m_mapDeviceParam[deviceID], "appNodeID");
            const auto& remoteProperty = ZGUtils::get(property, "channelAddr");
            appNodeDeviceProperties[appNodeID][deviceID][remoteProperty] = dataValue;
        }
        // 对于每一个应用节点下的设备，按照设备类型进行分类并发送
        for (auto& [appNodeID, deviceProperties] : appNodeDeviceProperties)
        {
            QJsonObject object;
            object["track_id"] = appNodeID.c_str();
            std::map<std::string, ZG6000::MapStringMap> deviceTypeProperties;
            for (auto& [deviceID, properties] : deviceProperties)
            {
                const auto& deviceType = ZGUtils::get(m_mapDeviceParam[deviceID], "typeID");
                const auto& mapDeviceType = ZGUtils::get(m_mapDeviceType, deviceType);
                deviceTypeProperties[mapDeviceType][deviceID] = std::move(properties);
            }
            // 按照设备类型生成json对象
            for (const auto& [remoteDeviceType, newDeviceProperties] : deviceTypeProperties)
            {
                QJsonArray deviceArray;
                for (const auto& [deviceID, properties] : newDeviceProperties)
                {
                    QJsonObject deviceObject;
                    deviceObject["id"] = deviceID.c_str();
                    for (const auto& [property, value] : properties)
                    {
                        deviceObject[property.c_str()] = value.c_str();
                    }
                    deviceArray.push_back(deviceObject);
                }
                object[remoteDeviceType.c_str()] = deviceArray;
            }
            QJsonArray regionDeviceArray;
            // 获取该应用节点下的所有区域及关联设备(mp_param_region表取id)
            QString sql = QString("SELECT id,deviceID FROM mp_param_region WHERE appNodeID = '%1'").arg(appNodeID.c_str());
            ZG6000::ListStringMap listRegion;
            if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listRegion))
            {
                ZGLOG_ERROR(QStringLiteral("获取区域信息失败"));
                return;
            }
            // 针对每个区域，获取该区域下的人员（mp_param_region_user表根据regionID获取userID）
            for (const auto& region : listRegion)
            {
                const auto& regionID = ZGUtils::get(region, "id");
                const auto& deviceID = ZGUtils::get(region, "deviceID");
                sql = QString("SELECT userID FROM mp_param_region_user WHERE regionID = '%1'").arg(regionID.c_str());
                ZG6000::StringList listUserID;
                if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
                {
                    ZGLOG_ERROR(QStringLiteral("获取区域用户ID失败"));
                    return;
                }
                QJsonArray userArray;
                for (const auto& userID : listUserID)
                {
                    userArray.push_back(userID.c_str());
                }
                QJsonObject regionObject;
                regionObject["id"] = deviceID.c_str();
                regionObject["is_there_anyone"] = userArray.size() > 0;
                regionObject["personnel_list"] = userArray;
                regionDeviceArray.push_back(regionObject);
                object["maintenance_platform"] = regionDeviceArray;
            }
            QJsonDocument doc(object);
            QByteArray ba = doc.toJson();
            ZGLOG_TRACE(ba);
            QString url = QString("http://%1/api/v1/safety_interlock/device/status/update").arg(m_hostAddress);
            postJson(rcDeviceStates, url, doc.toJson());
        }
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGEXNBWebClientMng::sendHeartbeat()
{
    QString url = QString("http://%1/api/v1/heartbeat").arg(m_hostAddress);
    ZGLOG_TRACE(url);
    {
        QReadLocker locker(&m_lock);
        if (!m_accessTokenValid)
        {
            ZGLOG_ERROR("access token invalid.");
            return;
        }
    }
    QList<QPair<QByteArray, QByteArray>> header;
    {
        QReadLocker locker(&m_lock);
        header.append(qMakePair(QByteArray("Authorization"), QString("Bearer %1").arg(m_accessToken).toLatin1()));
    }
    m_pHttpClient->get(rcHeartbeat, url, header);
}

bool ZGEXNBWebClientMng::checkAccessToken()
{
    QReadLocker locker(&m_lock);
    return m_accessTokenValid;
}

void ZGEXNBWebClientMng::postJson(int requestID, const QString& url, const QByteArray& data)
{
    {
        QReadLocker locker(&m_lock);
        if (!m_accessTokenValid)
        {
            ZGLOG_ERROR("access token invalid.");
            return;
        }
    }
    QList<QPair<QByteArray, QByteArray>> header;
    {
        QReadLocker locker(&m_lock);
        header.append(qMakePair(QByteArray("Authorization"), QString("Bearer %1").arg(m_accessToken).toLatin1()));
    }
    m_pHttpClient->postJson(requestID, url, header, data);
}

void ZGEXNBWebClientMng::processAuthorization(const QByteArray& data)
{
    ZGLOG_TRACE(QString("processAuthorization, data: %1").arg(data));
    QJsonDocument doc = QJsonDocument::fromJson(data);
    const auto& object = doc.object();
    if (object.find("access_token") == object.end())
    {
        ZGLOG_ERROR("access_token not found in response.");
        return;
    }
    QWriteLocker locker(&m_lock);
    m_accessToken = object["access_token"].toString();
    m_accessTokenValid = true;
}

void ZGEXNBWebClientMng::processUsers(const QByteArray& data)
{
    QJsonDocument doc = QJsonDocument::fromJson(data);
    const auto& root = doc.object();
    const auto& array = root["data"].toArray();
    ZGLOG_TRACE(QString("user size: %1").arg(array.size()));
    ZG6000::MapStringMap mapUser;
    const char* name = "name";
    const char* code = "code";
    const char* phone = "work_phone";
    const char* photo = "base64_img";
    const char* hash = "hash";
    const char* license = "climbing_work_permit";
    for (auto item : array)
    {
        ZG6000::StringMap user;
        const auto& object = item.toObject();
        for (auto it = object.begin(); it != object.end(); ++it)
        {
            const auto& key = it.key();
            const auto& value = it.value();
            user[key.toStdString()] = value.toVariant().toString().toStdString();
        }
        user[hash] = std::to_string(std::hash<std::string>()(user[photo]));
        mapUser[user["id"]] = user;
    }
    QString sql = QString("SELECT * FROM sp_param_hrm_user WHERE isSync = 1");
    ZG6000::MapStringMap mapUserLocal;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), mapUserLocal))
    {
        ZGLOG_ERROR(QStringLiteral("获取本地用户信息失败"));
        return;
    }
    ZG6000::StringList listSql;
    for (const auto& pair : mapUser)
    {
        const auto& id = pair.first;
        const auto& user = pair.second;
        auto it = mapUserLocal.find(id);
        if (it == mapUserLocal.end())
        {
            ZG6000::StringMap newUser;
            newUser["id"] = id;
            newUser["employeeNumber"] = ZGUtils::get(user, code);
            newUser["name"] = ZGUtils::get(user, name);
            newUser["mobileNumber"] = ZGUtils::get(user, phone);
            newUser["photoHash"] = ZGUtils::get(user, hash);
            // 照片存储在单独的文件内容表中，用户表中只存储文件内容ID和照片的hash值
            ZG6000::StringMap fileContent;
            std::string fileContentID;
            if (!ZGProxyCommon::createUUID(fileContentID))
            {
                ZGLOG_ERROR(QStringLiteral("创建文件内容ID失败"));
                return;
            }
            newUser["photoContentID"] = fileContentID;
            listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user", newUser));
            if (!addUserAppNode(listSql, id))
            {
                ZGLOG_ERROR(QStringLiteral("添加用户应用节点失败"));
                return;
            }
            if (!addUserAuth(listSql, id))
            {
                ZGLOG_ERROR(QStringLiteral("添加用户授权失败"));
                return;
            }
            if (!addOrChangeUserRole(listSql, id, ZGUtils::get(user, license) == "1"))
            {
                ZGLOG_ERROR(QStringLiteral("添加或修改用户角色失败"));
                return;
            }
            fileContent["id"] = fileContentID;
            fileContent["content"] = ZGUtils::get(user, photo);
            listSql.push_back(ZGUtils::generateInsertSql("sp_param_file_content", fileContent));
        }
        else
        {
            const auto& localUser = it->second;
            ZG6000::StringMap updateUser;
            updateUser["id"] = id;
            if (ZGUtils::get(user, name) != ZGUtils::get(localUser, "name"))
                updateUser["name"] = ZGUtils::get(user, name);
            if (ZGUtils::get(user, phone) != ZGUtils::get(localUser, "mobileNumber"))
                updateUser["mobileNumber"] = ZGUtils::get(user, phone);
            if (ZGUtils::get(user, code) != ZGUtils::get(localUser, "employeeNumber"))
                updateUser["employeeNumber"] = ZGUtils::get(user, code);
            if (ZGUtils::get(user, hash) != ZGUtils::get(localUser, "photoHash"))
            {
                updateUser["photoHash"] = ZGUtils::get(user, hash);
                // 更新照片，先查看用户表中是否存在文件内容ID，没有就创建
                const auto& fileContentID = ZGUtils::get(localUser, "photoContentID");
                if (fileContentID.empty())
                {
                    ZG6000::StringMap fileContent;
                    std::string newFileContentID;
                    if (!ZGProxyCommon::createUUID(newFileContentID))
                    {
                        ZGLOG_ERROR(QStringLiteral("创建文件内容ID失败"));
                        return;
                    }
                    updateUser["photoContentID"] = newFileContentID;
                    fileContent["id"] = newFileContentID;
                    fileContent["content"] = ZGUtils::get(user, photo);
                    listSql.push_back(ZGUtils::generateInsertSql("sp_param_file_content", fileContent));
                }
                else
                {
                    ZG6000::StringMap fileContent;
                    fileContent["id"] = fileContentID;
                    fileContent["content"] = ZGUtils::get(user, photo);
                    listSql.push_back(ZGUtils::generateUpdateSql("sp_param_file_content", fileContent));
                }
            }
            listSql.push_back(ZGUtils::generateUpdateSql("sp_param_hrm_user", updateUser));
        }
    }
    // 删除本地不存在的用户
    for (const auto& pair : mapUserLocal)
    {
        const auto& id = pair.first;
        const auto& user = pair.second;
        if (mapUser.find(id) == mapUser.end())
        {
            listSql.push_back(QString("DELETE FROM sp_param_hrm_user WHERE id = '%1'").arg(id.c_str()).toStdString());
            listSql.push_back(QString("DELETE FROM sp_param_hrm_user_appnode WHERE userID = '%1'").arg(id.c_str()).toStdString());
            listSql.push_back(QString("DELETE FROM sp_param_hrm_user_auth WHERE userID = '%1'").arg(id.c_str()).toStdString());
            listSql.push_back(QString("DELETE FROM sp_param_hrm_user_role WHERE userID = '%1'").arg(id.c_str()).toStdString());
            listSql.push_back(QString("DELETE FROM sp_param_hrm_user_finger WHERE userID = '%1'").arg(id.c_str()).toStdString());
            listSql.push_back(QString("DELETE FROM sp_param_hrm_user_card WHERE userID = '%1'").arg(id.c_str()).toStdString());
            // 删除文件内容表中的照片
            const auto& fileContentID = ZGUtils::get(user, "photoContentID");
            if (!fileContentID.empty())
                listSql.push_back(QString("DELETE FROM sp_param_file_content WHERE id = '%1'").arg(fileContentID.c_str()).toStdString());
        }
    }
    if (!listSql.empty())
    {
        if (!ZGProxyCommon::execBatchSql(listSql))
        {
            ZGLOG_ERROR(QStringLiteral("同步用户信息失败"));
        }
    }
}

void ZGEXNBWebClientMng::processHeartbeat(const QByteArray& data)
{
    ZGLOG_TRACE(data);
    QString message = QString("{\"id\":\"%1\",\"time\":\"%2\"}")
        .arg(m_clientID, ZGUtils::DateTimeToString(QDateTime::currentDateTime()));
    m_pMqttClient->sendPublish("ZG_T_CLIENT_HEART", message);
}

bool ZGEXNBWebClientMng::addUserAppNode(ZG6000::StringList& listSql, const std::string& userID)
{
    ZG6000::StringList listUserAppNodeID;
    if (!ZGProxyCommon::createUUID(static_cast<int>(m_listAppNodeID.size()), listUserAppNodeID))
    {
        ZGLOG_ERROR(QStringLiteral("创建用户应用节点ID失败"));
        return false;
    }
    for (const auto& appNodeID : listUserAppNodeID)
    {
        ZG6000::StringMap userAppNode;
        userAppNode["id"] = appNodeID;
        userAppNode["userID"] = userID;
        userAppNode["appNodeID"] = appNodeID;
        listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user_appnode", userAppNode));
    }
    return true;
}

bool ZGEXNBWebClientMng::addUserAuth(ZG6000::StringList& listSql, const std::string& userID)
{
    ZG6000::StringList listUserAuthID;
    if (!ZGProxyCommon::createUUID(2, listUserAuthID))
    {
        ZGLOG_ERROR(QStringLiteral("创建用户授权ID失败"));
        return false;
    }
    // 为用户固定增加ZG_AM_PASSWORD、ZG_AM_HIK_ALL两种授权方式
    ZG6000::StringMap userAuth;
    userAuth["id"] = listUserAuthID[0];
    userAuth["userID"] = userID;
    userAuth["authModeID"] = "ZG_AM_PASSWORD";
    listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user_auth", userAuth));
    userAuth["id"] = listUserAuthID[1];
    userAuth["authModeID"] = "ZG_AM_HIK_ALL";
    listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user_auth", userAuth));
    return true;
}

bool ZGEXNBWebClientMng::addOrChangeUserRole(ZG6000::StringList& listSql, const std::string& userID, bool hasLicense)
{
    // license代表登高作业权限，如果有登高作业权限，角色为ZG_HR_OPERATOR，否则为ZG_HR_MAINTAINER
    // 首先要先从用户角色表中查找是否存在该用户的角色，不存在则添加；如果存在，判断角色是否需要修改
    std::string roleID;
    if (hasLicense)
        roleID = "ZG_HR_OPERATOR";
    else
        roleID = "ZG_HR_MAINTAINER";
    QString sql = QString("SELECT id, roleID FROM sp_param_hrm_user_role WHERE userID = '%1'").arg(userID.c_str());
    ZG6000::ListStringMap listUserRole;
    if (!ZGProxyCommon::execQuerySql(sql.toStdString(), listUserRole))
    {
        ZGLOG_ERROR(QStringLiteral("查询用户角色失败"));
        return false;
    }
    if (listUserRole.empty())
    {
        std::string userRoleID;
        if (!ZGProxyCommon::createUUID(userRoleID))
        {
            ZGLOG_ERROR(QStringLiteral("创建用户角色ID失败"));
            return false;
        }
        ZG6000::StringMap userRole;
        userRole["id"] = userRoleID;
        userRole["userID"] = userID;
        userRole["roleID"] = roleID;
        listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user_role", userRole));
    }
    else
    {
        // 当前用户可能有多个角色，如果角色中已经包含了当前hasLicense所对应的角色，不需要添加
        bool needChange = true;
        for (const auto& userRole : listUserRole)
        {
            if (ZGUtils::get(userRole, "roleID") == roleID)
            {
                needChange = false;
                break;
            }
        }
        // 先删除相反状态的角色，再添加新的角色
        if (needChange)
        {
            std::string deleteRoleID;
            if (hasLicense)
                deleteRoleID = "ZG_HR_MAINTAINER";
            else
                deleteRoleID = "ZG_HR_OPERATOR";
            sql = QString("DELETE FROM sp_param_hrm_user_role WHERE userID = '%1' AND roleID = '%2'").arg(userID.c_str()).arg(deleteRoleID.c_str());
            listSql.push_back(sql.toStdString());
            std::string userRoleID;
            if (!ZGProxyCommon::createUUID(userRoleID))
            {
                ZGLOG_ERROR(QStringLiteral("创建用户角色ID失败"));
                return false;
            }
            ZG6000::StringMap userRole;
            userRole["id"] = userRoleID;
            userRole["userID"] = userID;
            userRole["roleID"] = roleID;
            listSql.push_back(ZGUtils::generateInsertSql("sp_param_hrm_user_role", userRole));
        }
    }
    return true;
}

void ZGEXNBWebClientMng::processDataProperty(const std::string& value, const ZG6000::StringMap& property)
{
    try
    {
        const auto& deviceID = ZGUtils::get(property, "domain");
        const auto& remoteProperty = ZGUtils::get(property, "channelAddr");
        ZG6000::StringMap deviceParam;
        if (!ZGProxyCommon::getDataByFields("mp_param_device", deviceID, {"appNodeID", "typeID"}, deviceParam))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备信息失败"));
            return;
        }
        const auto& appNodeID = ZGUtils::get(deviceParam, "appNodeID");
        const auto& deviceType = ZGUtils::get(deviceParam, "typeID");
        const auto& remoteDeviceType = ZGUtils::get(m_mapDeviceType, deviceType);
        QJsonObject object;
        object["track_id"] = appNodeID.c_str();
        QJsonArray deviceArray;
        QJsonObject deviceObject;
        deviceObject["id"] = deviceID.c_str();
        if (remoteProperty == "PeopleNum")
        {
            const auto& regionID = ZGUtils::get(m_mapDeviceRegion, deviceID);
            QString sql = QString("SELECT userID FROM mp_param_region_user WHERE regionID = '%1'").arg(regionID.c_str());
            ZG6000::StringList listUserID;
            if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listUserID))
            {
                ZGLOG_ERROR(QStringLiteral("获取区域用户ID失败"));
                return;
            }
            bool hasPeople = listUserID.size() > 0;
            deviceObject["is_there_anyone"] = hasPeople;
            QJsonArray userArray;
            for (const auto& userID : listUserID)
            {
                userArray.push_back(userID.c_str());
            }
            deviceObject["personnel_list"] = userArray;
        }
        else
            deviceObject[remoteProperty.c_str()] = value.c_str();
        deviceArray.push_back(deviceObject);
        object[remoteDeviceType.c_str()] = deviceArray;
        QJsonDocument doc(object);
        QString url = QString("http://%1/api/v1/safety_interlock/device/status/update").arg(m_hostAddress);
        QByteArray ba = doc.toJson();
        ZGLOG_TRACE(doc.toJson().data());
        postJson(rcDeviceStates, url, doc.toJson());
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

void ZGEXNBWebClientMng::processWarnDataProperty(const std::string& id, const std::string& value, const ZG6000::StringMap& property)
{
    try
    {
        const auto& deviceID = ZGUtils::get(property, "domain");
        ZG6000::StringMap deviceParam;
        if (!ZGProxyCommon::getDataByFields("mp_param_device", deviceID, {"appNodeID", "name"}, deviceParam))
        {
            ZGLOG_ERROR(QStringLiteral("获取设备信息失败"));
            return;
        }
        const auto& deviceName = ZGUtils::get(deviceParam, "name");
        const auto& appNodeID = ZGUtils::get(deviceParam, "appNodeID");
        std::string alarmName;
        if (!ZGProxyCommon::getDataByField("mp_param_dataset_yx", id, "name", alarmName))
        {
            ZGLOG_ERROR(QStringLiteral("获取告警名称失败"));
            return;
        }
        QJsonObject object;
        object["track_id"] = appNodeID.c_str();
        QJsonArray alarmArray;
        QJsonObject alarmObject;
        alarmObject["name"] = deviceName.c_str();
        std::string alarmStatus;
        if (value == "2")
            alarmStatus = u8"发生";
        else
            alarmStatus = u8"消失";
        alarmObject["value"] = (alarmName + alarmStatus).c_str();
        alarmArray.push_back(alarmObject);
        object["alarm_list"] = alarmArray;
        QJsonDocument doc(object);
        QByteArray ba = doc.toJson();
        QString url = QString("http://%1/api/v1/safety_interlock/alarm/info").arg(m_hostAddress);
        ZGLOG_TRACE(url);
        ZGLOG_TRACE(ba.data());
        postJson(rcDeviceStates, url, doc.toJson());
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
    }
}

bool ZGEXNBWebClientMng::checkState()
{
    return m_initialized;
}

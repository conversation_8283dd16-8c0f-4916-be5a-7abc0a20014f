#ifndef ZGEXNBWEBCLIENTMNG_H
#define ZGEXNBWEBCLIENTMNG_H

#include <QObject>
#include <QTimer>
#include <QMap>
#include <QReadWriteLock>
#include "ZGServerCommon.h"
#include "ZGHttpClient.h"

class ZGMqttClient;
class ZGEXNBWebClientMng : public QObject
{
    Q_OBJECT
public:
    static ZGEXNBWebClientMng* instance();
    void init();
    bool checkState();
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ZG6000::ListRecord listRecord);

private slots:
    void onTimer();
    void onReceivedData(int requestId, QByteArray data, int statusCode);

private:
    explicit ZGEXNBWebClientMng(QObject *parent = nullptr);
    void initEvents();
    bool initServerInstConfig();
    bool initServerInstInfo();
    bool initParams();
    bool initMqttClient();
    bool initPublishPoints();
    void requestAuthorization();
    void requestUsers();
    void publishDeviceStates();
    void sendHeartbeat();
    bool checkAccessToken();
    enum RequestCode
    {
        rcAuthorization = 1,
        rcUsers,
        rcTaskState,
        rcDeviceStates,
        rcWarnStates,
        rcHeartbeat
    };
    void postJson(int requestID,
                  const QString& url, const QByteArray& data);
    void processAuthorization(const QByteArray& data);
    void processUsers(const QByteArray& data);
    void processHeartbeat(const QByteArray& data);
    bool addUserAppNode(ZG6000::StringList& listSql, const std::string& userID);
    bool addUserAuth(ZG6000::StringList& listSql, const std::string& userID);
    bool addOrChangeUserRole(ZG6000::StringList& listSql, const std::string& userID, bool hasLicense);
    void processDataProperty(const std::string& value, const ZG6000::StringMap& property);
    void processWarnDataProperty(const std::string& id, const std::string& value, const ZG6000::StringMap& property);

private:
    bool m_initialized{ false };
    QString m_serverName{ "" };
    QString m_instName{ "" };
    std::string m_localNodeID;
    int m_initInterval{ 10 };
    int m_checkInterval{ 10 };
    QString m_hostAddress;
    QTimer m_timer;
    QString m_accessToken;
    QString m_portMapID;
    QString m_portWarnMapID;
    QString m_clientID;
    ZG6000::StringList m_listAppNodeID;
    ZG6000::StringMap m_mapDeviceType;
    ZG6000::StringMap m_mapDeviceRegion;
    ZG6000::MapStringMap m_mapDeviceParam;
    ZG6000::MapStringMap m_mapDataProperty;
    ZG6000::MapStringMap m_mapWarnDataProperty;
    bool m_accessTokenValid{ false };
    QReadWriteLock m_lock;
    ZGMqttClient* m_pMqttClient{nullptr};
    ZGHttpClient* m_pHttpClient{nullptr};
};

#endif // ZGEXNBWEBCLIENTMNG_H

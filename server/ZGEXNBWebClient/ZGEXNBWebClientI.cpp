#include "ZGEXNBWebClientI.h"
#include "ZGEXNBWebClientMng.h"

namespace ZG6000 {

ZGEXNBWebClientI::ZGEXNBWebClientI()
{
    ZGEXNBWebClientMng::instance()->init();
}

bool ZGEXNBWebClientI::checkState(const Ice::Current &current)
{
    return ZGEXNBWebClientMng::instance()->checkState();
}

void ZGEXNBWebClientI::dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current)
{
    ZGEXNBWebClientMng::instance()->dispatchData(std::move(tableName), std::move(oper), std::move(reason), std::move(time), std::move(listRecord));
}

} // namespace ZG6000

#ifndef ZG6000_ZGEXNBWEBCLIENTI_H
#define ZG6000_ZGEXNBWEBCLIENTI_H

#include <ZGEXNBWebClient.h>

namespace ZG6000 {

class ZGEXNBWebClientI : public ZG6000::ZGEXNBWebClient
{
public:
    ZGEXNBWebClientI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current &current) override;
    void dispatchData(std::string tableName, std::string oper, std::string reason, std::string time, ListRecord listRecord, const Ice::Current &current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGEXNBWEBCLIENTI_H

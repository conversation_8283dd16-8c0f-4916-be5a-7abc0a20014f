#ifndef ZGSTCDCBRANCH_H
#define ZGSTCDCBRANCH_H

#include "ZGSTEndPoint.h"

/** @brief	排流柜支路 */
class ZGSTSensor;
class ZGSTCDCBranch : public ZGSTEndPoint
{
	Q_OBJECT
public:
    explicit ZGSTCDCBranch(const std::string& id, QObject *parent = nullptr);
	void addSensor(ZGSTSensor* sensor) { m_lstSensor.push_back(sensor); }

public:
	bool initialize() override;
	void onProcessAutoPL();

private:
	bool initPLGDevice();
	void processAutoPL();

private:
	std::vector<ZGSTSensor*> m_lstSensor;
	std::string m_plgDeviceID;
};

#endif // ZGSTCDCBRANCH_H

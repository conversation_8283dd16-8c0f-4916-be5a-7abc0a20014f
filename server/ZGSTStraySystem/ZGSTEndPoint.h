#ifndef ZGSTENDPOINT_H
#define ZGSTENDPOINT_H

#include <QObject>
#include "ZGProxyCommon.h"

class ZGSTEndPoint : public QObject
{
    Q_OBJECT
public:
    explicit ZGSTEndPoint(const std::string& id, QObject *parent = nullptr);
    void processPropertyChange(const ZG6000::MapStringMap& _properties);
    virtual bool initialize() { return true; }
    const std::string& id() { return m_id; }
    enum TimeType { ttDay, ttHour, ttM30, ttBt };
    void resetPropertyValue(const std::string& propertyName, TimeType timeType, const QDateTime& currTime);

protected:
    std::string m_id;
    std::unordered_map<std::string, std::function<void(const ZG6000::StringMap&)>> m_mapProcessor;
};

#endif // ZGSTENDPOINT_H

#ifndef ZG6000_ZGSTSTRAYSYSTEMI_H
#define ZG6000_ZGSTSTRAYSYSTEMI_H

#include <ZGSTStraySystem.h>

namespace ZG6000 {

class ZGSTStraySystemI : public ZG6000::ZGSTStraySystem
{
public:
    ZGSTStraySystemI();

    // ZGServerBase interface
public:
    bool checkState(const Ice::Current& current) override;

    // ZGSTStraySystem interface
public:
    bool setMeasureStation(std::string clientID, std::string inStationID, std::string outStationID, ErrorInfo& e, const Ice::Current& current) override;
    bool calculateOffset(ErrorInfo& e, const Ice::Current& current) override;
    bool startCalculate(ErrorInfo& e, const Ice::Current& current) override;
    bool stopCalculate(ErrorInfo& e, const Ice::Current& current) override;
    bool getValidStations(ListStringMap& listMapStation, ErrorInfo& e, const Ice::Current& current) override;
    bool getSystemParam(StringMap& systemParam, ErrorInfo& e, const Ice::Current& current) override;
    bool setSystemParam(StringMap systemParam, ErrorInfo& e, const Ice::Current& current) override;
};

} // namespace ZG6000

#endif // ZG6000_ZGSTSTRAYSYSTEMI_H

#include "ZGSTCDCBranch.h"

#include "ZGSTSensor.h"
#include "ZGSTStrayDefine.h"
#include "ZGSTStraySystemMng.h"
#include "ZGSTSystem.h"
#include "ZGUtils.h"

ZGSTCDCBranch::ZGSTCDCBranch(const std::string& id, QObject* parent)
	: ZGSTEndPoint(id, parent)
{
}

bool ZGSTCDCBranch::initialize()
{
	if (!initPLGDevice())
		return false;
	return true;
}

bool ZGSTCDCBranch::initPLGDevice()
{
	std::string tableName;
	std::string dataID;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getDataIDByProperty(m_id, PL_YX_QF, tableName, dataID, e))
    {
        ZGLOG_ERROR(e);
		return false;
    }
	std::string datasetID;
	if (!ZGProxyCommon::getDataByField(tableName, dataID, "datasetID", datasetID))
		return false;
	QString sql = QString("SELECT id FROM mp_param_device WHERE datasetID = '%1'").arg(datasetID.c_str());
	if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), m_plgDeviceID))
		return false;
	return true;
}


void ZGSTCDCBranch::onProcessAutoPL()
{
	processAutoPL();
}

void ZGSTCDCBranch::processAutoPL()
{
	std::string qfValue;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, PL_YX_QF, qfValue, e))
    {
        ZGLOG_ERROR(e);
		return;
    }
	bool plSignal = false;
	for (auto sensor: m_lstSensor)
	{
		if (sensor->plSignal())
		{
			ZGLOG_DEBUG(QString("branch '%1' sensor '%2' signal").arg(m_id.c_str()).arg(sensor->id().c_str()));
			plSignal = true;
			break;
		}
	}
	auto straySystem = dynamic_cast<ZGSTSystem*>(parent());
	if (plSignal)
	{
		if (qfValue == "1")
		{
			std::string RemoteControl;
            if (!ZGProxyCommon::getPropertyValue(m_plgDeviceID, "RemoteControl", RemoteControl, e))
            {
                ZGLOG_ERROR(e);
				return;
            }
			if (RemoteControl != "2")
				return;
			if (!straySystem->autoDrainage())
				return;
			ZG6000::ErrorInfo e;
			if (!ZG6000::ZGSTStraySystemMng::instance()->sendPLCommand("-1", m_id, PL_YK_CLOSE, "2", true, e))
				ZGLOG_ERROR(e);
		}
	}
	else
	{
		if (qfValue == "2")
		{
			std::string RemoteControl;
            if (!ZGProxyCommon::getPropertyValue(m_plgDeviceID, "RemoteControl", RemoteControl, e))
            {
                ZGLOG_ERROR(e);
				return;
            }
			if (RemoteControl != "2")
				return;
			if (!straySystem->autoDrainage())
				return;
			ZG6000::ErrorInfo e;
			if (!ZG6000::ZGSTStraySystemMng::instance()->sendPLCommand("-1", m_id, PL_YK_OPEN, "2", true, e))
				ZGLOG_ERROR(e);
		}
	}
}

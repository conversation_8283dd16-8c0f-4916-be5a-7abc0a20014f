#include "ZGSTStraySystemI.h"
#include "ZGSTStraySystemMng.h"

namespace ZG6000 {

ZGSTStraySystemI::ZGSTStraySystemI()
{
    ZGSTStraySystemMng::instance()->init();
}

bool ZG6000::ZGSTStraySystemI::checkState(const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->checkState();
}

bool ZG6000::ZGSTStraySystemI::setMeasureStation(std::string clientID, std::string inStationID, std::string outStationID, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->setMeasureStation(std::move(clientID), std::move(inStationID), std::move(outStationID), e);
}

bool ZG6000::ZGSTStraySystemI::calculateOffset(ErrorInfo& e, const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->calculateOffset(e);
}

bool ZG6000::ZGSTStraySystemI::startCalculate(ErrorInfo& e, const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->startCalculate(e);
}

bool ZG6000::ZGSTStraySystemI::stopCalculate(ErrorInfo& e, const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->stopCalculate(e);
}

bool ZG6000::ZGSTStraySystemI::getValidStations(ListStringMap& listMapStation, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->getValidStations(listMapStation, e);
}

bool ZG6000::ZGSTStraySystemI::getSystemParam(StringMap& systemParam, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->getSystemParam(systemParam, e);
}

bool ZG6000::ZGSTStraySystemI::setSystemParam(StringMap systemParam, ErrorInfo& e, const Ice::Current& current)
{
    return ZGSTStraySystemMng::instance()->setSystemParam(std::move(systemParam), e);
}

} // namespace ZG6000

#include "ZGSTSensor.h"

#include <csignal>

#include "ZGSTStrayDevice.h"
#include "ZGSTStrayDefine.h"
#include "ZGSTStraySystemMng.h"
#include "ZGSTSystem.h"
#include "ZGUtils.h"

ZGSTSensor::ZGSTSensor(const std::string& sensorID, QObject* parent):
	ZGSTEndPoint(sensorID, parent)
{
}

bool ZGSTSensor::initialize()
{
	return initPLWarn();
}

void ZGSTSensor::onNewMinute()
{
    std::string bt, bt1;
    ZG6000::ErrorInfo e;
    ZG6000::StringMap mapValue;
    if (!ZGProxyCommon::getPropertyValues(m_id, {CALC_YC_BTDW, ST_YC_BTDW}, mapValue, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    if (std::abs(std::atof(mapValue[CALC_YC_BTDW].c_str()) - std::atof(mapValue[ST_YC_BTDW].c_str())) > 0.000001)
    {
        std::string ysProp = std::string("CMD_") + ST_YC_BTDW;
        ZG6000::ErrorInfo e;
        if (!ZG6000::ZGSTStraySystemMng::instance()->sendYsCommand("-1", m_id, ysProp, mapValue[CALC_YC_BTDW], true, e))
            ZGLOG_ERROR(e);
    }
    std::string rtState;
    if (ZGProxyCommon::getPropertyValue(m_id, ST_FIELD_STATE, rtState, e))
        ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_COMM_STATE, rtState, e);
}

bool ZGSTSensor::initPLWarn()
{
	QDateTime currentTime = QDateTime::currentDateTime();
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YX_JHPY_P_WARN, m_lastWarnValue, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    if (m_lastWarnValue == "1")
    {
        m_warnResetTime = QDateTime::currentDateTime();
    }
	auto straySystem = dynamic_cast<ZGSTSystem*>(parent());
	std::string yxValue;
    if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YX_PL_SIGNAL, yxValue, e))
	{
        ZGLOG_ERROR(e);
		return false;
	}
	if (yxValue == "2" && (currentTime.time() > straySystem->operationEnd() || currentTime.time() < straySystem->operationStart()))
	{
		resetWarnCalc();
		ZGLOG_DEBUG(QStringLiteral("告警已投入，当前不在运营时段，复归, sensorID: %1").arg(m_id.c_str()));
		return true;
	}
	ZG6000::MapStringMap mapValue;
    if (!ZGProxyCommon::getProperties(m_id, {
		CALC_YC_PL_WARN_SUM_D1, CALC_YC_PL_WARN_SUM_D2, CALC_YC_PL_WARN_SUM_D3, CALC_YC_PL_WARN_SUM_D4,
		CALC_YC_PL_WARN_SUM_D5, CALC_YC_PL_WARN_SUM_D6, CALC_YC_PL_WARN_SUM_D7
    }, mapValue, e))
	{
		ZGLOG_ERROR(QStringLiteral("获取传感器'%1'遥测属性失败").arg(m_id.c_str()));
		return false;
	}
	for (int i = 0; i < straySystem->maxCalcDays(); ++i)
	{
        QString propertyName = QString("Ud_P_Warn_D%1_SUM").arg(i + 1);
		auto propertyValue = mapValue[propertyName.toStdString()]["rtNewValue"];
		auto rtUpdateTime = mapValue[propertyName.toStdString()]["rtUpdateTime"];
		if (!propertyValue.empty())
		{
			QDateTime dt;
			ZGUtils::StringToDateTime(rtUpdateTime.c_str(), dt);
			if (dt.date() == currentTime.date())
			{
				m_warnIndex = i + 1;
				ZGLOG_DEBUG(QStringLiteral("当前天数:'%1', sensorID: %2").arg(m_warnIndex).arg(m_id.c_str()));
				return true;
			}
		}
		else
		{
			if (i == 0)
			{
				resetWarnCalc();
				ZGLOG_DEBUG(QStringLiteral("当前为第一天, sensorID: %1").arg(m_id.c_str()));
				return true;
			}
			// 获取前日数据
            propertyName = QString("Ud_P_Warn_D%1_SUM").arg(i);
			propertyValue = mapValue[propertyName.toStdString()]["rtNewValue"];
			rtUpdateTime = mapValue[propertyName.toStdString()]["rtUpdateTime"];
			QDateTime dt;
			ZGUtils::StringToDateTime(rtUpdateTime.c_str(), dt);
			// 刚好相差一天
			if (dt.daysTo(currentTime) == 1)
			{
				// 前日告警已经超过指定小时数，继续累加天数
				if (std::atoi(propertyValue.c_str()) > straySystem->maxWarnHours())
				{
					m_warnIndex = i + 1;
					ZGLOG_DEBUG(QStringLiteral("前日告警已经超过指定小时数，继续累加天数, index = '%1', sensorID: %2").arg(m_warnIndex).arg(m_id.c_str()));
					return true;
				}
			}
			// 相差已经大于一天
			resetWarnCalc();
			ZGLOG_DEBUG(QStringLiteral("相差已经大于一天, 复位告警天数, sensorID: %1").arg(m_id.c_str()));
			return true;
		}
	}
	resetWarnCalc();
	return true;
}

void ZGSTSensor::calcPLOn()
{
	ZGLOG_DEBUG(QStringLiteral("计算排流逻辑"));
	QDateTime currTime = QDateTime::currentDateTime();
    QString propertyName = QString("Ud_P_Warn_D%1_SUM").arg(m_warnIndex);
	ZG6000::StringMap sumProp;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getProperty(m_id, propertyName.toStdString(), sumProp, e))
	{
		ZGLOG_WARN(QStringLiteral("获取传感器'%1'属性'%2'失败").arg(m_id.c_str()).arg(propertyName));
		return;
	}
    int value = static_cast<int>(std::atof(sumProp["rtNewValue"].c_str()) * 2);
	auto straySystem = dynamic_cast<ZGSTSystem*>(parent());
	if (currTime.time() > straySystem->operationEnd() || currTime.time() < straySystem->operationStart())
	{
		ZGLOG_DEBUG(QStringLiteral("超过运营时间, id: '%1'").arg(m_id.c_str()));
        if (!sumProp["rtNewValue"].empty())
		{
			if (value < straySystem->maxWarnHours())
			{
				ZGLOG_DEBUG(QStringLiteral("当日告警时长未超过设定值，复归...."));
				resetWarnCalc();
			}
		}
		return;
	}
	if (m_warnIndex >= straySystem->maxCalcDays() && value >= straySystem->maxWarnHours() * 2)
    {
        if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_PL_SIGNAL, "2", e, true))
            ZGLOG_ERROR(e);
    }
	ZG6000::StringMap warnP;
    if (!getWarnInfo(CALC_YX_JHPY_P_WARN, warnP))
	{
        ZGLOG_WARN(QStringLiteral("获取传感器'%1'告警属性'%2'信息失败").arg(m_id.c_str()).arg(CALC_YX_JHPY_P_WARN));
		return;
	}
	QDateTime dtSum;
    ZGLOG_DEBUG(QStringLiteral("更新时间: '%1', id: '%2'").arg(sumProp["rtUpdateTime"].c_str()).arg(m_id.c_str()));
    if (!sumProp["rtNewValue"].empty() && !sumProp["rtUpdateTime"].empty())
	{
        if (ZGUtils::StringToDateTime(sumProp["rtUpdateTime"].c_str(), dtSum) &&
			isSameM30DateTime(dtSum, currTime))
		{
			ZGLOG_DEBUG(QStringLiteral("该周期内已执行过计算逻辑，返回"));
			return;
		}
	}
    if (!ZGUtils::StringToDateTime(sumProp["rtUpdateTime"].c_str(), dtSum))
		return;
    if (dtSum.date() != currTime.date() && !sumProp["rtNewValue"].empty())
	{
		if (value < straySystem->maxWarnHours())
		{
			ZGLOG_DEBUG("前日告警未达到告警小时数，复归......");
			resetWarnCalc();
			value = 0;
		}
		else
		{
			ZGLOG_DEBUG(QString("sum date: '%1', curr date: '%2'").arg(dtSum.date().toString("yyyy-MM-dd")).arg(currTime.date().toString("yyyy-MM-dd")));
			ZGLOG_DEBUG(QStringLiteral("增加告警天数, id: '%1'").arg(m_id.c_str()));
			value = 0;
			++m_warnIndex;
		}
	}
    if (warnP["rtNewValue"] == "2")
	{
        propertyName = QString("Ud_P_Warn_D%1_SUM").arg(m_warnIndex);
		++value;
		double hours = static_cast<double>(value) / 2.0;
		QString strHour = QString::number(hours, 'f', 2);
		ZGLOG_DEBUG(QStringLiteral("告警天数: '%1', 告警小时数: '%2', id: '%3'").arg(m_warnIndex).arg(strHour).arg(m_id.c_str()));
        if (ZGProxyCommon::updatePropertyValue(m_id, propertyName.toStdString(), strHour.toStdString(), e, true))
		{
			if (m_warnIndex >= straySystem->maxCalcDays() && value >= (straySystem->maxWarnHours() * 2))
            {
				ZGLOG_DEBUG(QString("设置排流告警信号, id: '%1'").arg(m_id.c_str()));
                if (!ZGProxyCommon::updatePropertyValue(m_id, CALC_YX_PL_SIGNAL, "2", e, true))
                        ZGLOG_ERROR(e);
            }
		}
        else
            ZGLOG_ERROR(e);
	}
}

void ZGSTSensor::calcPLOff()
{
	ZGLOG_DEBUG(QStringLiteral("执行排流复归逻辑"));
	QDateTime currTime = QDateTime::currentDateTime();
	auto straySystem = dynamic_cast<ZGSTSystem*>(parent());
	if (currTime.time() > straySystem->operationEnd() || currTime.time() < straySystem->operationStart())
	{
		ZGLOG_DEBUG(QStringLiteral("超出运营时间，复归，id: '%1'").arg(m_id.c_str()));
		resetWarnCalc();
		return;
	}
//    ZG6000::StringMap warnProp;
//    ZG6000::ErrorInfo e;
//    if (!ZGProxyCommon::getProperty(m_id, CALC_YX_JHPY_P_WARN, warnProp, e))
//    {
//        ZGLOG_ERROR(e);
//        return;
//    }
//    if (warnProp["rtUpdateTime"].empty())
//        return;
//    QDateTime dtWarn;
//    if (!ZGUtils::StringToDateTime(warnProp["rtUpdateTime"].c_str(), dtWarn))
//        return;
    // QString propertyName = QString("Ud_P_Warn_D%1_SUM").arg(m_warnIndex);
 //    ZG6000::StringMap sumProp;
 //    if (!ZGProxyCommon::getProperty(m_id, propertyName.toStdString(), sumProp))
	// {
	// 	ZGLOG_WARN(QStringLiteral("获取传感器'%1'属性'%2'失败").arg(m_id.c_str()).arg(propertyName));
	// 	return;
	// }
    // if (sumProp["rtNewValue"].empty() || sumProp["rtUpdateTime"].empty())
	// 	return;
	// QDateTime dtSum;
    // if (!ZGUtils::StringToDateTime(sumProp["rtUpdateTime"].c_str(), dtSum))
	// 	return;
//    if ((warnProp["rtNewValue"] == "1") && (dtWarn.secsTo(currTime) >= static_cast<qint64>(straySystem->warnOffHours()) * 60 * 60))
//	{
//		ZGLOG_DEBUG(QStringLiteral("告警消失小时数超过设定值，复归, id: '%1'").arg(m_id.c_str()));
//		resetWarnCalc();
//	}
    if (m_lastWarnValue == "1" && m_warnResetTime.secsTo(currTime) >= static_cast<qint64>(straySystem->warnOffHours()) * 60 * 60)
    {
        ZGLOG_DEBUG(QStringLiteral("告警消失小时数超过设定值，复归, id: '%1'").arg(m_id.c_str()));
        resetWarnCalc();
    }
}

void ZGSTSensor::onCalculateSignal()
{
    ZG6000::ErrorInfo e;
    std::string warnValue;
    if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YX_JHPY_P_WARN, warnValue, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    if (warnValue == "1" && m_lastWarnValue == "2")
    {
        m_warnResetTime = QDateTime::currentDateTime();
    }
    m_lastWarnValue = warnValue;
	if (plSignal())
		calcPLOff();
	else
		calcPLOn();
}

bool ZGSTSensor::plSignal() const
{
	std::string plSig;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValue(m_id, CALC_YX_PL_SIGNAL, plSig, e))
    {
        ZGLOG_ERROR(e);
		return false;
    }
	return (plSig == "2");
}

bool ZGSTSensor::getWarnInfo(const std::string& propertyName, ZG6000::StringMap& warnProp)
{
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getProperty(m_id, propertyName, warnProp, e))
	{
        ZGLOG_ERROR(e);
		return false;
	}
	return true;
}

void ZGSTSensor::resetWarnCalc()
{
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::updatePropertyValues(m_id, {
        {CALC_YC_PL_WARN_SUM_D1, "0"}, {CALC_YC_PL_WARN_SUM_D2, "0"}, {CALC_YC_PL_WARN_SUM_D3, "0"}, {CALC_YC_PL_WARN_SUM_D4, "0"},
        {CALC_YC_PL_WARN_SUM_D5, "0"}, {CALC_YC_PL_WARN_SUM_D6, "0"}, {CALC_YC_PL_WARN_SUM_D7, "0"}, {CALC_YX_PL_SIGNAL, "1"}
        }, e, true))
    {
        ZGLOG_ERROR(QStringLiteral("更新设备'%1'排流统计数据失败").arg(m_id.c_str()));
        return;
    }
    m_warnIndex = 1;
}

bool ZGSTSensor::isSameM30DateTime(const QDateTime& dt1, const QDateTime& dt2)
{
    return (dt1.date() == dt2.date() && dt1.time().hour() == dt2.time().hour()
        && (dt1.time().minute() / 30 == dt2.time().minute() / 30));
}

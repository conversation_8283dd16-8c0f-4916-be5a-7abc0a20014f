#ifndef ZGSTSENSOR_H
#define ZGSTSENSOR_H

#include <QObject>
#include <QReadWriteLock>
#include "ZGSTEndPoint.h"

class ZGSTSensor : public ZGSTEndPoint
{
	Q_OBJECT
public:
	explicit ZGSTSensor(const std::string& sensorID, QObject* parent = nullptr);
	bool initialize() override;
    void onNewMinute();
	void onNewMinute30();
	
	void onCalculateSignal();
	void onNewHour();
	void onNewDay();
	void onNewBtTime();
	bool plSignal() const;
	void resetCalculation();

private:
	bool initPLWarn();
	void calcPLOn();
	void calcPLOff();
	void resetWarnCalc();
	bool isSameM30DateTime(const QDateTime& dt1, const QDateTime& dt2);
    bool getWarnInfo(const std::string& propertyName, ZG6000::StringMap& warnProp);

private:
	std::unordered_map<std::string, std::string> m_cacheData;
	std::vector<double> m_cacheValue;
	QReadWriteLock m_lock;
	int m_warnIndex{ -1 };
    std::string m_lastWarnValue;
    QDateTime m_warnResetTime;
};

#endif // ZGSTSENSOR_H

#include "ZGSTSystem.h"
#include "ZGSTCDCBranch.h"
#include "ZGSTSensor.h"

#include <QtConcurrent>

#include "ZGDebugMng.h"
#include "ZGProxyCommon.h"
#include "ZGRuntime.h"
#include "ZGSTStrayDefine.h"
#include "ZGSTStraySystemMng.h"
#include "ZGUtils.h"
#include "zgerror/ZGSTStraySystemError.h"

ZGSTSystem::ZGSTSystem(QObject *parent)
    : QObject{parent}
{

}

bool ZGSTSystem::initialize()
{
    initServerInstConfig();
    if (!initMqttClient())
        return false;
    if (!initPropertyDataID())
        return false;
    if (!initCDCBranch())
        return false;
    updateGlobalParam();
    m_checkTimer.setTimerType(Qt::PreciseTimer);
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSTSystem::onProcess);
    m_updateTimer.setTimerType(Qt::PreciseTimer);
    connect(&m_updateTimer, &QTimer::timeout, this, &ZGSTSystem::onUpdateData);
    m_checkTimer.start(60 * 1000);
    connect(this, &ZGSTSystem::ctrlTimer, this, &ZGSTSystem::onCtrlTimer);
    return true;
}

bool ZGSTSystem::setMeasureStation(const std::string& clientID, const std::string& inStationID, const std::string& outStationID, ZG6000::ErrorInfo& e)
{
    return checkAndExecute([&]()-> bool
        {
            std::unique_lock locker(m_mutex);
            m_clientID = clientID;
            m_inputCurrentStation = inStationID;
            m_outputCurrentStation = outStationID;
            if (!calculateCurrentModel())
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
                e.errDetail = "Calculate sensor model error.";
                ZGLOG_ERROR(e);
                return false;
            }
            if (!startMeasure(e))
            {
                ZGLOG_ERROR("Start measure error.");
                return false;
            }
            auto pair = m_mapPropertyDataID.find(CALC_YC_GDDZ);
            if (pair == m_mapPropertyDataID.end())
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
                e.errDetail = QStringLiteral("找不到指定的属性'%1'").arg(CALC_YC_GDDZ).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZG6000::StringMap record;
            record["rtNewValue"] = "0";
            QString dateTime = ZGUtils::DateTimeToString(QDateTime::currentDateTime());
            record["rtUpdateTime"] = dateTime.toStdString();
            if (!ZGProxyCommon::updateDataByID("mp_param_dataset_yc", pair->second, record))
                ZGLOG_ERROR("clear resistance value error.");
            m_isCalculating = true;
            emit ctrlTimer(true);
            return true;
        }, e);
}

bool ZGSTSystem::calculateOffset(ZG6000::ErrorInfo& e)
{
    if (!setSensorOffsetValues(m_upCurrentInSensor, PREFIX_UP_IN, e))
        return false;
    if (!setSensorOffsetValues(m_upCurrentOutSensor, PREFIX_UP_OUT, e))
        return false;
    if (!setSensorOffsetValues(m_downCurrentInSensor, PREFIX_DOWN_IN, e))
        return false;
    if (!setSensorOffsetValues(m_downCurrentOutSensor, PREFIX_DOWN_OUT, e))
        return false;
    return true;
}

bool ZGSTSystem::startCalculate(ZG6000::ErrorInfo& e)
{
    if (m_upCurrentInSensor.empty() || m_upCurrentOutSensor.empty()
        || m_downCurrentInSensor.empty() || m_downCurrentOutSensor.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("无效的传感器参数").toStdString();
        ZGLOG_ERROR(e);
        stopCalculate();
        return false;
    }
    auto future = QtConcurrent::run([&]
        {
            publishCalculateState(CS_START, 0, "");
            if (!checkDeviceState())
            {
                ZGLOG_ERROR("Invalid sensor or device state");
                publishCalculateState(CS_ERROR, 0, QStringLiteral("无效的传感器或设备状态"));
            }
            std::vector<double> listResistance;
            for (int i = 0; i < m_calculateCount; ++i)
            {
                if (m_stopCalculate)
                {
                    stopCalculate();
                    return;
                }
                double resistance;
                if (!calculateResistance(resistance))
                {
                    ZGLOG_ERROR("calculateResistance error");
                    publishCalculateState(CS_ERROR, 0, QStringLiteral("计算轨地电阻出错，停止计算......"));
                    stopCalculate();
                    return;
                }
                listResistance.push_back(resistance);
                double progress = (i + 1) / static_cast<double>(m_calculateCount);
                publishCalculateState(CS_EXECUTING, static_cast<int>(progress * 100), "");
                QThread::msleep(m_calculateCount * 1000);
            }
            double totalResistance{0.0};
            for (const auto& resistance : listResistance)
            {
                totalResistance += resistance;
            }
            ZGLOG_DEBUG(QString("totalResistance: %1").arg(totalResistance));
            double avgResistance = totalResistance / static_cast<double>(listResistance.size());
            QString strResistance = QString::number(avgResistance, 'f', 2);
            if (!updateYcPropertyValue(CALC_YC_GDDZ, strResistance.toStdString(), QDateTime::currentDateTime()))
            {
                stopCalculate();
                return;
            }
            stopCalculate();
            publishCalculateState(CS_FINISH, 0, strResistance);
        });
    return true;
}

bool ZGSTSystem::stopCalculate(ZG6000::ErrorInfo& e)
{
    if (m_inputCurrentStation.empty() || (m_outputCurrentStation.empty()))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("输入或输出站点为空").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (!stopCalculate())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("停止计算出错").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSTSystem::getValidStations(ZG6000::ListStringMap& listMapStation, ZG6000::ErrorInfo& e)
{
    std::string sql = "SELECT DISTINCT a.id, a.name FROM sp_param_appnode a LEFT JOIN mp_param_device b ON a.id = b.appNodeID "
                      "WHERE b.isEnable = '1' AND b.typeID = 'ZG_DT_ZS_SENSOR'";
    if (!ZGProxyCommon::execQuerySql(sql, listMapStation))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取站点信息失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSTSystem::getSystemParam(ZG6000::StringMap& systemParam, ZG6000::ErrorInfo& e)
{
    const auto& instID = ZGRuntime::instance()->getInstanceID();
    std::string logicalName;
    if (!ZGProxyCommon::getDataByField("sp_param_node_service_instance", instID.toStdString(), "logicalName", logicalName))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取服务实例'%1'逻辑名称失败").arg(instID).toStdString();
        ZGLOG_ERROR(e);
    }
    QString sql = QString("SELECT a.propertyName AS id, b.rtNewValue FROM mp_param_model_param a "
        "LEFT JOIN mp_param_dataset_param b ON a.id = b.dataModelID "
        "LEFT JOIN mp_param_dataset c ON b.datasetID = c.id "
        "WHERE c.serviceLogicalName = '%1'").arg(logicalName.c_str());
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), systemParam))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_DB);
        e.errDetail = QStringLiteral("获取系统参数失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    return true;
}

bool ZGSTSystem::setSystemParam(const ZG6000::StringMap& systemParam, ZG6000::ErrorInfo& e)
{
    const auto& instID = ZGRuntime::instance()->getInstanceID();
    std::string logicalName;
    if (!ZGProxyCommon::getDataByField("sp_param_node_service_instance", instID.toStdString(), "logicalName", logicalName))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_RT);
        e.errDetail = QStringLiteral("获取服务实例'%1'逻辑名称失败").arg(instID).toStdString();
        ZGLOG_ERROR(e);
    }
    ZG6000::StringList listSql;
    for (const auto& [name, value] : systemParam)
    {
        QString sql = QString("SELECT a.id FROM mp_param_dataset_param a "
            "LEFT JOIN mp_param_model_param b ON a.dataModelID = b.id "
            "LEFT JOIN mp_param_dataset c ON a.datasetID = c.id "
            "WHERE c.serviceLogicalName = '%1' AND b.propertyName = '%2'")
            .arg(logicalName.c_str()).arg(name.c_str());
        std::string paramID;
        if (!ZGProxyCommon::execQuerySqlField(sql.toStdString(), paramID))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_DB);
            e.errDetail = QStringLiteral("获取属性'%1'关联参数ID失败").arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (paramID.empty())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
            e.errDetail = QStringLiteral("未找到属性'%1'关联参数ID").arg(name.c_str()).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        listSql.push_back(ZGUtils::generateUpdateSql("mp_param_dataset_param", {{"id", paramID}, {"rtNewValue", value}}));
    }
    if (listSql.empty())
        return true;
    if (!ZGProxyCommon::execBatchSql(listSql))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_DB);
        e.errDetail = QStringLiteral("更新参数数据失败").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    ZGProxyCommon::synchronize();
    return true;
}

void ZGSTSystem::onProcess()
{
    processPL();
    updateGlobalParam();
    if (m_isSyncOperTime)
        syncOperTime();
}

void ZGSTSystem::onUpdateData()
{
    double upInVol, upInIa, upInIb, upOutVol, upOutIa, upOutIb,
        downInVol, downInIa, downInIb, downOutVol, downOutIa, downOutIb;
    updateSensorValues(m_upCurrentInSensor, PREFIX_UP_IN, upInVol, upInIa, upInIb);
    updateSensorValues(m_upCurrentOutSensor, PREFIX_UP_OUT, upOutVol, upOutIa, upOutIb);
    updateSensorValues(m_downCurrentInSensor, PREFIX_DOWN_IN, downInVol, downInIa, downInIb);
    updateSensorValues(m_downCurrentOutSensor, PREFIX_DOWN_OUT, downOutVol, downOutIa, downOutIb);
}

void ZGSTSystem::onCtrlTimer(bool start)
{
    if (start)
        m_updateTimer.start(1000);
    else
        m_updateTimer.stop();
}

bool ZGSTSystem::initMqttClient()
{
    if (!ZGRuntime::instance()->initMqttClient(ZGRuntime::MQTT_MESSAGE))
    {
        ZGLOG_ERROR("initMqttClient error.");
        return false;
    }
    m_mqttClient = ZGRuntime::instance()->getMqttClientMessage();
    if (m_mqttClient == nullptr)
    {
        ZGLOG_ERROR("getMqttClientMessage error.");
        return false;
    }
    m_mqttClient->connectToHost();
    return true;
}

void ZGSTSystem::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "sync_operation_time", value, 0, 1, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_isSyncOperTime = value;
}

bool ZGSTSystem::initPropertyDataID()
{
    std::string calcDataset;
    if (!getDatasetOfServiceInst(calcDataset))
        return false;
    QString sql = QString("SELECT b.propertyName AS id, a.id AS dataID FROM mp_param_dataset_yc a "
        "LEFT JOIN mp_param_model_yc b ON a.dataModelID = b.id "
        "WHERE a.datasetID = '%1'").arg(calcDataset.c_str());
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), m_mapPropertyDataID))
    {
        ZGLOG_ERROR(QStringLiteral("获取数据集'%1'遥测属性失败").arg(calcDataset.c_str()));
        return false;
    }
    sql = QString("SELECT b.propertyName AS id, a.id AS dataID FROM mp_param_dataset_param a "
        "LEFT JOIN mp_param_model_param b ON a.dataModelID = b.id "
        "WHERE a.datasetID = '%1'").arg(calcDataset.c_str());
    ZG6000::StringMap mapPropertyParamID;
    if (!ZGProxyCommon::execQuerySqlPair(sql.toStdString(), mapPropertyParamID))
    {
        ZGLOG_ERROR(QStringLiteral("获取数据集'%1'参数属性失败").arg(calcDataset.c_str()));
        return false;
    }
    for (const auto& [id, name] : mapPropertyParamID)
    {
        m_mapPropertyDataID[id] = name;
    }
    return true;
}

bool ZGSTSystem::initCDCBranch()
{
    ZGLOG_DEBUG("initCDCBranch");
    QString sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_PLGZL' AND isEnable = '1'";
    ZG6000::StringList listCDCBranchID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listCDCBranchID))
    {
        ZGLOG_ERROR(QStringLiteral("获取排流柜支路列表失败"));
        return false;
    }
    std::unordered_map<std::string, ZGSTSensor*> mapSensor;
    for (const auto& cdcBranchId : listCDCBranchID)
    {
        auto cdcBranch = new ZGSTCDCBranch(cdcBranchId, this);
        m_listCDCBranch.push_back(cdcBranch);
        sql = QString("SELECT a.dstDeviceID FROM mp_param_device_relation a "
                      "LEFT JOIN mp_param_device b ON a.dstDeviceID = b.id "
                      "WHERE srcDeviceID = '%1' AND b.isEnable = 1")
                .arg(cdcBranchId.c_str());
        ZG6000::StringList listSensor;
        if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listSensor))
        {
            ZGLOG_ERROR(QStringLiteral("获取排流柜支路'%1'关联传感器失败").arg(cdcBranchId.c_str()));
            return false;
        }
        for (auto&& sensorID : listSensor)
        {
            auto pair = mapSensor.find(sensorID);
            if (pair == mapSensor.end())
            {
                ZGSTSensor* sensor = new ZGSTSensor(sensorID, this);
                if (!sensor->initialize())
                {
                    ZGLOG_ERROR(QStringLiteral("初始化传感器'%1'错误").arg(sensorID.c_str()));
                    delete sensor;
                    return false;
                }
                mapSensor[sensorID] = sensor;
                m_listSensor.push_back(sensor);
                cdcBranch->addSensor(sensor);
            }
            else
                cdcBranch->addSensor(pair->second);
        }
        if (!cdcBranch->initialize())
        {
            ZGLOG_ERROR(QStringLiteral("排流支路'%1'初始化错误").arg(cdcBranchId.c_str()));
            return false;
        }
    }
    return true;
}

bool ZGSTSystem::updateGlobalParam()
{
    updateParamVariable(PARAM_UD_P_WARN_SUM, m_maxWarnHours);
    updateParamVariable(PARAM_UD_P_WARN_DAY_SUM, m_maxCalcDays);
    updateParamVariable(PARAM_UD_P_WARN_OFF_SUM, m_warnOffHours);
    updateParamVariable(PARAM_AUTO_DRAINAGE, m_autoDrainage);
    const auto& operStartID = m_mapPropertyDataID[PARAM_OPER_START];
    std::string operStartValue;
    if (!ZGProxyCommon::getDataByField("mp_param_dataset_param", operStartID, "rtNewValue", operStartValue))
        return false;
    QTime startTime = QTime::fromString(operStartValue.c_str(), "hh:mm:ss");
    if (!startTime.isValid())
    {
        ZGLOG_ERROR(QStringLiteral("无效的开始时间'%1'").arg(operStartValue.c_str()));
        return false;
    }
    m_operStartTime = startTime;
    const auto& operEndID = m_mapPropertyDataID[PARAM_OPER_END];
    std::string operEndValue;
    if (!ZGProxyCommon::getDataByField("mp_param_dataset_param", operEndID, "rtNewValue", operEndValue))
        return false;
    QTime endTime = QTime::fromString(operEndValue.c_str(), "hh:mm:ss");
    if (!endTime.isValid())
    {
        ZGLOG_ERROR(QStringLiteral("无效的结束时间'%1'").arg(operEndValue.c_str()));
        return false;
    }
    m_operEndTime = endTime;
    return true;
}

bool ZGSTSystem::updateParamVariable(const std::string& paramName, int& variable)
{
    const auto& paramID = m_mapPropertyDataID[paramName];
    std::string paramValue;
    if (!ZGProxyCommon::getDataByField("mp_param_dataset_param", paramID, "rtNewValue", paramValue))
        return false;
    int value = std::atoi(paramValue.c_str());
    variable = value;
    return true;
}

void ZGSTSystem::processPL()
{
    for (auto sensor : m_listSensor)
    {
        sensor->onCalculateSignal();
    }
    for (auto cdcBranch : m_listCDCBranch)
    {
        cdcBranch->onProcessAutoPL();
    }
}

void ZGSTSystem::syncOperTime()
{
    std::string sql = "SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_STRAY_DEV' AND isEnable = 1";
    ZG6000::StringList listDevID;
    if (!ZGProxyCommon::execQuerySqlCol(sql, listDevID))
        return;
    const auto& operStartID = m_mapPropertyDataID[PARAM_OPER_START];
    std::string currentStartTime;
    ZG6000::ErrorInfo e;
    if (ZGProxyCommon::getDataByField("mp_param_dataset_param", operStartID, "rtNewValue", currentStartTime))
    {
        for (const auto& devID : listDevID)
        {
            std::string operStart;
            if (!ZGProxyCommon::getPropertyValue(devID, PARAM_OPER_START, operStart, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }
            ZG6000::ErrorInfo e;
            if (operStart != currentStartTime)
            {
                if (!ZG6000::ZGSTStraySystemMng::instance()->sendYsCommand("-1", devID, YS_OPER_START, currentStartTime, false, e))
                    ZGLOG_ERROR(e);
            }
        }
    }
    const auto& operEndID = m_mapPropertyDataID[PARAM_OPER_END];
    std::string currentEndTime;
    if (ZGProxyCommon::getDataByField("mp_param_dataset_param", operEndID, "rtNewValue", currentEndTime))
    {
        for (const auto& devID : listDevID)
        {
            std::string operEnd;
            if (!ZGProxyCommon::getPropertyValue(devID, PARAM_OPER_END, operEnd, e))
            {
                ZGLOG_ERROR(e);
                continue;
            }
            ZG6000::ErrorInfo e;
            if (operEnd != currentEndTime)
            {
                if (!ZG6000::ZGSTStraySystemMng::instance()->sendYsCommand("-1", devID, YS_OPER_END, currentEndTime, false, e))
                    ZGLOG_ERROR(e);
            }
        }
    }
}

bool ZGSTSystem::calculateCurrentModel()
{
    if (m_inputCurrentStation.empty() || m_outputCurrentStation.empty())
    {
        ZGLOG_ERROR("Invalid station selection.");
        return false;
    }
    ZG6000::ListStringMap inputUpSensors, inputDownSensors;
    if (!getCurrentSensorList(m_inputCurrentStation, inputUpSensors, inputDownSensors))
        return false;
    if (inputUpSensors.empty())
    {
        ZGLOG_ERROR(QString("Input station %1 has no up sensors").arg(m_inputCurrentStation.c_str()));
        return false;
    }
    if (inputDownSensors.empty())
    {
        ZGLOG_ERROR(QString("Input station %1 has no down sensors").arg(m_inputCurrentStation.c_str()));
        return false;
    }
    ZG6000::ListStringMap outputUpSensors, outputDownSensors;
    if (!getCurrentSensorList(m_outputCurrentStation, outputUpSensors, outputDownSensors))
        return false;
    if (outputUpSensors.empty())
    {
        ZGLOG_ERROR(QString("Output station %1 has no up sensors").arg(m_outputCurrentStation.c_str()));
        return false;
    }
    if (outputDownSensors.empty())
    {
        ZGLOG_ERROR(QString("Output station %1 has no down sensors").arg(m_outputCurrentStation.c_str()));
        return false;
    }
    sortSensorList(inputUpSensors);
    sortSensorList(inputDownSensors);
    sortSensorList(outputUpSensors);
    sortSensorList(outputDownSensors);
    if (std::atof(inputUpSensors[0]["position"].c_str()) < std::atof(outputUpSensors[0]["position"].c_str()))
    {
        m_upCurrentInSensor = inputUpSensors[inputUpSensors.size() - 1]["id"];
        m_upCurrentOutSensor = outputUpSensors[0]["id"];
        m_downCurrentInSensor = inputDownSensors[inputDownSensors.size() - 1]["id"];
        m_downCurrentOutSensor = outputDownSensors[0]["id"];
    }
    else
    {
        m_upCurrentInSensor = inputUpSensors[0]["id"];
        m_upCurrentOutSensor = outputUpSensors[outputUpSensors.size() - 1]["id"];
        m_downCurrentInSensor = inputDownSensors[0]["id"];
        m_downCurrentOutSensor = outputDownSensors[outputDownSensors.size() - 1]["id"];
    }
    double distance{0.0};
    if (calculateTwoSensorsDistance(m_upCurrentInSensor, m_upCurrentOutSensor, distance))
    {
        QString dist = QString::number(distance, 'f', 2);
        updateYcPropertyValue(CALC_YC_DISTANCE, dist.toStdString(), QDateTime::currentDateTime());
    }
    ZGLOG_DEBUG(QString("up in sensor: %1").arg(m_upCurrentInSensor.c_str()));
    ZGLOG_DEBUG(QString("up out sensor: %1").arg(m_upCurrentOutSensor.c_str()));
    ZGLOG_DEBUG(QString("down in sensor: %1").arg(m_downCurrentInSensor.c_str()));
    ZGLOG_DEBUG(QString("down out sensor: %1").arg(m_downCurrentOutSensor.c_str()));
    return true;
}

bool ZGSTSystem::getCurrentSensorList(const std::string& stationID, ZG6000::ListStringMap& listUpSensors, ZG6000::ListStringMap& listDownSensors)
{
    ZG6000::StringList listSensor;
    std::string sql = "SELECT id FROM mp_param_device WHERE subtypeID = 'ZG_DS_MCS' AND isEnable = '1' AND appNodeID = '" + stationID + "'";
    if (!ZGProxyCommon::execQuerySqlCol(sql, listSensor))
    {
        ZGLOG_ERROR("Get station current sensors error.");
        return false;
    }
    ZG6000::ErrorInfo e;
    for (const auto& sensor : listSensor)
    {
        std::string direction;
        if (!ZGProxyCommon::getPropertyValue(sensor, "Direction", direction, e))
        {
            ZGLOG_ERROR(e);
            continue;
        }
        std::string position;
        if (!ZGProxyCommon::getPropertyValue(sensor, "Mileage", position, e))
        {
            ZGLOG_ERROR(e);
            continue;
        }
        if (direction == "1")
            listUpSensors.push_back(ZG6000::StringMap{{"id", sensor}, {"position", position}});
        else
            listDownSensors.push_back(ZG6000::StringMap{{"id", sensor}, {"position", position}});
    }
    return true;
}

void ZGSTSystem::sortSensorList(ZG6000::ListStringMap& listSensors)
{
    std::sort(listSensors.begin(), listSensors.end(), [&](const ZG6000::StringMap& left,
        const ZG6000::StringMap& right)
        {
            return std::atof(left.at("position").c_str()) < std::atof(right.at("position").c_str());
        });
}

bool ZGSTSystem::checkAndExecute(std::function<bool()> func, ZG6000::ErrorInfo& e)
{
    if (m_isCalculating)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
        e.errDetail = QStringLiteral("计算正在执行......").toStdString();
        ZGLOG_ERROR(e);
        return false;
    }
    if (func)
        return func();
    return true;
}

bool ZGSTSystem::getDeviceByStation(const std::string& stationID, std::string& deviceID)
{
    QString sql = QString("SELECT id FROM mp_param_device WHERE typeID = 'ZG_DT_STRAY_DEV' "
        "AND isEnable = '1' AND appNodeID = '%1'").arg(stationID.c_str());
    ZG6000::StringList listDeviceID;
    if (!ZGProxyCommon::execQuerySqlCol(sql.toStdString(), listDeviceID))
    {
        ZGLOG_ERROR(QStringLiteral("获取站点'%1'杂散装置失败").arg(stationID.c_str()));
        return false;
    }
    if (listDeviceID.empty())
    {
        ZGLOG_ERROR(QStringLiteral("站点'%1'无可用杂散装置").arg(stationID.c_str()));
        return false;
    }
    deviceID = listDeviceID[0];
    return true;
}

bool ZGSTSystem::sendCommandToDevice(const std::string& commandProp)
{
    ZG6000::StringList listDevice;
    std::string inputDevice;
    if (!getDeviceByStation(m_inputCurrentStation, inputDevice))
    {
        ZGLOG_ERROR(QString("Get station %1 device error.").arg(m_inputCurrentStation.c_str()));
        return false;
    }
    listDevice.push_back(inputDevice);
    std::string outputDevice;
    if (!getDeviceByStation(m_outputCurrentStation, outputDevice))
    {
        ZGLOG_ERROR(QString("Get station %1 device error.").arg(m_outputCurrentStation.c_str()));
        return false;
    }
    listDevice.push_back(outputDevice);
    for (const auto& device : listDevice)
    {
        ZG6000::ErrorInfo e;
        if (!ZG6000::ZGSTStraySystemMng::instance()->sendYkCommand(m_clientID, device, commandProp, "2", false, e))
            ZGLOG_ERROR(e);
    }
    return true;
}

bool ZGSTSystem::getDatasetOfServiceInst(std::string& calcDataset)
{
    QString serviceInstID = ZGRuntime::instance()->getInstanceID();
    std::string logicalName;
    if (!ZGProxyCommon::getDataByField("sp_param_node_service_instance", serviceInstID.toStdString(), "logicalName", logicalName))
        return false;
    std::string sql = "SELECT id FROM mp_param_dataset WHERE serviceLogicalName = '" + logicalName + "'";
    if (!ZGProxyCommon::execQuerySqlField(sql, calcDataset))
    {
        ZGLOG_ERROR(QStringLiteral("获取服务ID关联数据集失败"));
        return false;
    }
    return true;
}

bool ZGSTSystem::updateSensorValues(const std::string& sensorID, const std::string& prefix, double& gdVol, double& ggIa, double& ggIb)
{
    ZG6000::StringMap mapValue;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getPropertyValues(sensorID, {ST_YC_GDDY, CALC_YC_ZXADL, CALC_YC_ZXBDL}, mapValue, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    std::string gddyOffset;
    if (!getYcPropertyValue(prefix + CALC_YC_GDDY_OFFSET, gddyOffset))
        return false;
    std::string zxadlOffset;
    if (!getYcPropertyValue(prefix + CALC_YC_ZXA_OFFSET, zxadlOffset))
        return false;
    std::string zxbdlOffset;
    if (!getYcPropertyValue(prefix + CALC_YC_ZXB_OFFSET, zxbdlOffset))
        return false;
    QDateTime currTime = QDateTime::currentDateTime();
    gdVol = std::atof(mapValue[ST_YC_GDDY].c_str()) - std::atof(gddyOffset.c_str());
    QString strValue = QString::number(gdVol, 'f', 2);
    if (!updateYcPropertyValue(prefix + CALC_YC_GDDY_ACTUAL, strValue.toStdString(), currTime))
        return false;
    ggIa = std::atof(mapValue[CALC_YC_ZXADL].c_str()) - std::atof(zxadlOffset.c_str());
    strValue = QString::number(ggIa, 'f', 2);
    if (!updateYcPropertyValue(prefix + CALC_YC_ZXA_ACTUAL, strValue.toStdString(), currTime))
        return false;
    ggIb = std::atof(mapValue[CALC_YC_ZXBDL].c_str()) - std::atof(zxbdlOffset.c_str());
    strValue = QString::number(ggIb, 'f', 2);
    if (!updateYcPropertyValue(prefix + CALC_YC_ZXB_ACTUAL, strValue.toStdString(), currTime))
        return false;
    return true;
}

bool ZGSTSystem::setSensorOffsetValues(const std::string& sensorID, const std::string& prefix, ZG6000::ErrorInfo& e)
{
    ZG6000::StringMap mapValue;
    if (!ZGProxyCommon::getPropertyValues(sensorID, {ST_YC_GDDY, CALC_YC_ZXADL, CALC_YC_ZXBDL}, mapValue, e))
    {
        ZGLOG_ERROR(e);
        return false;
    }
    QDateTime currTime = QDateTime::currentDateTime();
    if (!updateYcPropertyValue(prefix + CALC_YC_GDDY_OFFSET, mapValue[ST_YC_GDDY], currTime))
        return false;
    if (!updateYcPropertyValue(prefix + CALC_YC_ZXA_OFFSET, mapValue[CALC_YC_ZXADL], currTime))
        return false;
    if (!updateYcPropertyValue(prefix + CALC_YC_ZXB_OFFSET, mapValue[CALC_YC_ZXBDL], currTime))
        return false;
    return true;
}

bool ZGSTSystem::getYcPropertyValue(const std::string& propertyName, std::string& value)
{
    auto pair = m_mapPropertyDataID.find(propertyName);
    if (pair == m_mapPropertyDataID.end())
    {
        ZGLOG_DEBUG(QString("Can't find property '%1'").arg(propertyName.c_str()));
        return false;
    }
    if (!ZGProxyCommon::getDataByField("mp_param_dataset_yc", pair->second, "rtNewValue", value))
        return false;
    return true;
}

bool ZGSTSystem::updateYcPropertyValue(const std::string& propertyName, const std::string& value, const QDateTime& dt)
{
    auto pair = m_mapPropertyDataID.find(propertyName);
    if (pair == m_mapPropertyDataID.end())
    {
        ZGLOG_DEBUG(QString("Can't find property '%1'").arg(propertyName.c_str()));
        return false;
    }
    if (!ZGProxyCommon::updateDataByID("mp_param_dataset_yc", pair->second, {
        {"rtNewValue", value},
        {"rtUpdateTime", ZGUtils::DateTimeToString(dt).toStdString()}
        }))
        return false;
    return true;
}

bool ZGSTSystem::startMeasure(ZG6000::ErrorInfo& e)
{
    return checkAndExecute([&]()-> bool
        {
            if (m_inputCurrentStation.empty() || (m_outputCurrentStation.empty()))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
                e.errDetail = "Current station is empty.";
                ZGLOG_ERROR(e);
                return false;
            }
            if (!sendCommandToDevice(CALC_YK_START_MEAS))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSTStraySystem::ZG_ERR_INTERNAL);
                e.errDetail = "Send command to device error.";
                ZGLOG_ERROR(e);
                return false;
            }
            return true;
        }, e);
}

bool ZGSTSystem::calculateResistance(double& resistance)
{
    double upInVol, upInIa, upInIb, upOutVol, upOutIa, upOutIb,
        downInVol, downInIa, downInIb, downOutVol, downOutIa, downOutIb;
    if (!updateSensorValues(m_upCurrentInSensor, PREFIX_UP_IN, upInVol, upInIa, upInIb))
    {
        publishCalculateState(CS_ERROR, 0, QStringLiteral("获取上行流入传感器数据失败"));
        return false;
    }
    if (!updateSensorValues(m_upCurrentOutSensor, PREFIX_UP_OUT, upOutVol, upOutIa, upOutIb))
    {
        publishCalculateState(CS_ERROR, 0, QStringLiteral("获取上行流出传感器数据失败"));
        return false;
    }
    if (!updateSensorValues(m_downCurrentInSensor, PREFIX_DOWN_IN, downInVol, downInIa, downInIb))
    {
        publishCalculateState(CS_ERROR, 0, QStringLiteral("获取下行流入传感器数据失败"));
        return false;
    }
    if (!updateSensorValues(m_downCurrentOutSensor, PREFIX_DOWN_OUT, downOutVol, downOutIa, downOutIb))
    {
        publishCalculateState(CS_ERROR, 0, QStringLiteral("获取下行流出传感器数据失败"));
        return false;
    }
    double avgVol = (upInVol + upOutVol + downInVol + downOutVol) / 4;
    double lossCurrent = (upInIa + upInIb + downInIa + downInIb) - (upOutIa + upOutIb + downOutIa + downOutIb);
    double distance{0.0};
    if (!calculateTwoSensorsDistance(m_upCurrentInSensor, m_upCurrentOutSensor, distance))
    {
        publishCalculateState(CS_ERROR, 0, QStringLiteral("计算传感器距离失败"));
        ZGLOG_ERROR("calculateTwoSensorsDistance error.");
        return false;
    }
    ZGLOG_DEBUG(QString("avgVol: %1, lossCurrent: %2, distance: %3").arg(avgVol).arg(lossCurrent).arg(distance));
    if (std::abs(lossCurrent - 0.0) < 0.000001)
    {
        publishCalculateState(CS_ERROR, 0, QStringLiteral("泄漏电流为0，无法计算轨地电阻"));
        ZGLOG_ERROR("loss current is equal 0, can't calculate.");
        return false;
    }
    resistance = (avgVol / lossCurrent) * (distance / 1000);
    QJsonObject obj;
    obj["avgVol"] = avgVol;
    obj["lossCurrent"] = lossCurrent;
    obj["distance"] = distance;
    obj["resistance"] = resistance;
    QJsonDocument doc(obj);
    publishCalculateState(CS_REPORT, 0, "", doc.toJson());
    ZGLOG_DEBUG(QString("resistance value: %1").arg(resistance));
    return true;
}

void ZGSTSystem::publishCalculateState(CalculateState state, size_t progress, const QString& errMsg, const QString& detail)
{
    QJsonObject obj;
    obj["state"] = static_cast<int>(state);
    obj["progress"] = static_cast<int>(progress);
    obj["desc"] = errMsg;
    obj["detail"] = detail;
    QJsonDocument doc(obj);
    QString topic = QString("%1/stray").arg(m_clientID.c_str());
    m_mqttClient->sendPublish(topic, doc.toJson());
}

bool ZGSTSystem::calculateTwoSensorsDistance(const std::string& upInSensorID, const std::string& upOutSensorID, double& distance)
{
    try
    {
        ZG6000::StringMap mapValue;
        ZG6000::ErrorInfo e;
        std::string upInPos;
        if (!ZGProxyCommon::getPropertyValue(upInSensorID, "Mileage", upInPos, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        std::string upOutPos;
        if (!ZGProxyCommon::getPropertyValue(upOutSensorID, "Mileage", upOutPos, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        double upInMileage = std::atof(upInPos.c_str());
        double upOutMileage = std::atof(upOutPos.c_str());
        distance = std::abs(upInMileage - upOutMileage);
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGSTSystem::checkDeviceState()
{
    std::string inputDeviceID;
    if (!getDeviceByStation(m_inputCurrentStation, inputDeviceID))
        return false;
    std::string deviceState;
    if (!ZGProxyCommon::getDataByField("mp_param_device", inputDeviceID, "rtState", deviceState))
        return false;
    if (deviceState != "2")
        return false;
    std::string outputDeviceID;
    if (!getDeviceByStation(m_outputCurrentStation, outputDeviceID))
        return false;
    if (!ZGProxyCommon::getDataByField("mp_param_device", outputDeviceID, "rtState", deviceState))
        return false;
    if (deviceState != "2")
        return false;
    return true;
}

void ZGSTSystem::clearCalculateData(const std::string& prefix)
{
    QDateTime currTime = QDateTime::currentDateTime();
    updateYcPropertyValue(prefix + CALC_YC_GDDY_OFFSET, "0", currTime);
    updateYcPropertyValue(prefix + CALC_YC_ZXA_OFFSET, "0", currTime);
    updateYcPropertyValue(prefix + CALC_YC_ZXB_OFFSET, "0", currTime);
    updateYcPropertyValue(prefix + CALC_YC_GDDY_ACTUAL, "0", currTime);
    updateYcPropertyValue(prefix + CALC_YC_ZXA_ACTUAL, "0", currTime);
    updateYcPropertyValue(prefix + CALC_YC_ZXB_ACTUAL, "0", currTime);
}

bool ZGSTSystem::stopCalculate()
{
    ZGLOG_DEBUG("stopCalculate");
    m_isCalculating = false;
    emit ctrlTimer(false);
    clearCalculateData(PREFIX_UP_IN);
    clearCalculateData(PREFIX_UP_OUT);
    clearCalculateData(PREFIX_DOWN_IN);
    clearCalculateData(PREFIX_DOWN_OUT);
    if (!sendCommandToDevice(CALC_YK_STOP_MEAS))
    {
        ZGLOG_ERROR("Send command to device error.");
        return false;
    }
    m_stopCalculate = false;
    return true;
}

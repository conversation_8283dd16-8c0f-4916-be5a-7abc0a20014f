#include "ZGSTEndPoint.h"
#include "ZGUtils.h"

ZGSTEndPoint::ZGSTEndPoint(const std::string& id, QObject *parent)
    : QObject{parent},
	m_id(id)
{

}

void ZGSTEndPoint::processPropertyChange(const ZG6000::MapStringMap& _properties)
{
	for (const auto & property : _properties)
	{
		auto pair = m_mapProcessor.find(property.first);
		if (pair != m_mapProcessor.end())
			pair->second(property.second);
    }
}

void ZGSTEndPoint::resetPropertyValue(const std::string &propertyName, TimeType timeType, const QDateTime &currTime)
{
    ZG6000::StringMap property;
    ZG6000::ErrorInfo e;
    if (!ZGProxyCommon::getProperty(m_id, propertyName, property, e))
    {
        ZGLOG_ERROR(e);
        return;
    }
    const auto& rtUpdateTime = property["updateTime"];
    QDateTime lastTime;
    if (ZGUtils::StringToDateTime(rtUpdateTime.c_str(), lastTime))
    {
        switch (timeType)
        {
        case ttBt:
        case ttDay:
            if (currTime.date().day() == lastTime.date().day())
                return;
        case ttHour:
            if (currTime.date().day() == lastTime.date().day() &&
                currTime.time().hour() == lastTime.time().hour())
                return;
        case ttM30:
            if (currTime.date().day() == lastTime.date().day() &&
                currTime.time().hour() == lastTime.time().hour() &&
                ((currTime.time().minute() / 30) == (lastTime.time().minute() / 30)))
                return;
        }
    }
    if (!ZGProxyCommon::updatePropertyValue(m_id, propertyName, "0", e, true))
        ZGLOG_ERROR(e);
}

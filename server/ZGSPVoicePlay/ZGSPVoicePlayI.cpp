#include "ZGSPVoicePlayI.h"
#include "ZGSPVoicePlayMng.h"
#include <QDebug>

ZG6000::ZGSPVoicePlayI::ZGSPVoicePlayI()
{
    ZGSPVoicePlayMng::instance()->init();
}

bool
ZG6000::ZGSPVoicePlayI::checkState(const Ice::Current& current)
{
    Q_UNUSED(current)
    return ZGSPVoicePlayMng::instance()->checkState();
}

void
ZG6000::ZGSPVoicePlayI::play(::std::string alarmLevelID,
                             ::std::string speechText,
                             const Ice::Current& current)
{
    Q_UNUSED(current)
    ZGSPVoicePlayMng::instance()->play(alarmLevelID, speechText);
}

void
ZG6000::ZGSPVoicePlayI::playMulti(ListStringMap listVoice,
                                  const Ice::Current& current)
{
    Q_UNUSED(current)
    ZGSPVoicePlayMng::instance()->playMulti(listVoice);
}

void
ZG6000::ZGSPVoicePlayI::speak(::std::string speechText,
                              const Ice::Current& current)
{
    Q_UNUSED(current)
    qDebug() << "speak";
    ZGSPVoicePlayMng::instance()->speak(speechText);
}

void
ZG6000::ZGSPVoicePlayI::speakCount(::std::string speechText,
                                   int repeatCount,
                                   const Ice::Current& current)
{
    Q_UNUSED(current)
    ZGSPVoicePlayMng::instance()->speakCount(speechText, repeatCount);
}

void
ZG6000::ZGSPVoicePlayI::start(const Ice::Current& current)
{
    Q_UNUSED(current)
    ZGSPVoicePlayMng::instance()->start();
}

void
ZG6000::ZGSPVoicePlayI::stop(const Ice::Current& current)
{
    Q_UNUSED(current)
    ZGSPVoicePlayMng::instance()->stop();
}

void
ZG6000::ZGSPVoicePlayI::clear(const Ice::Current& current)
{
    Q_UNUSED(current)
    ZGSPVoicePlayMng::instance()->clear();
}

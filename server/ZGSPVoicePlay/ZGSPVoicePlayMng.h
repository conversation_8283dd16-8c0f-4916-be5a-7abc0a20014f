#ifndef ZGSPVOICEPLAYMNG_H
#define ZGSPVOICEPLAYMNG_H

#include <QThread>
#include <QTimer>
#include <QSoundEffect>
#include "QtTextToSpeech/QTextToSpeech"
#include <mutex>
#include <deque>
#include <unordered_map>
#include "ZGProxyMng.h"
#include "ZGServerCommon.h"

class ZGSPVoicePlayMng : public QThread
{
    Q_OBJECT
public:
    static ZGSPVoicePlayMng* instance();

    void init();

    bool checkState();

    /**
     * @brief   播放指定告警等级的声音
     *
     * @param   alarmLevelID   告警等级
     * @param   speechText     语音文本内容
     */
    void play(const std::string& alarmLevelID, const std::string& speechText);

    /**
     * @brief   播放声音列表
     *
     * @param   listVoice   声音列表，每个map中包含两个元素，第一个元素key为alarmLevelID, 值为告警等级的ID
     *                      第二个元素key为speechText，值为语音文本
     */
    void playMulti(const ZG6000::ListStringMap& listVoice);

    /**
     * @brief   立即播放语音
     *
     * @param   speechText    语音文本.
     */
    void speak(const std::string& speechText);

    /**
     * @brief   立即播放指定次数的语音
     *
     * @param   speechText     语音文本
     * @param   repeatCount    重复次数
     */
    void speakCount(const std::string& speechText, int repeatCount);

    /** @brief   开始语音播放 */
    void start();

    /** @brief   停止语音播放 */
    void stop();

    /** @brief   清除语音播放 */
    void clear();

protected:
    void run() override;

private:
    enum PlayType
    {
        ptNone, ptPlayFile, ptPlayTTS
    };

    struct AlarmLevel
    {
        bool isPlayFile{false};
        bool isPlayTTS{false};
        int playCount{0};
        QSoundEffect* soundEffect{nullptr};
    };

    struct Voice
    {
        PlayType playType{PlayType::ptNone};
        std::string alarmLevelID;        
        std::string content;
        int playCount{0};
    };

private:
    explicit ZGSPVoicePlayMng(QObject *parent = nullptr);
    void initTextToSpeech();
    void initEvents();
    void initServerInstConfig();
    bool initServerInstInfo();
    void playVoice(const std::string& alarmLevelID, const std::string& speechText);
    void doPlay(const Voice& voice);
    bool getAvailableVoice(Voice& voice);
    bool initAlarmLevelParam();

signals:
    void initFinished();
    void stoping();
    void clearing();

private slots:
    void onInitFinished();
    void onCheckStatus();
    void onTrigger();
    void onPlayingChanged();
    void onStateChanged(QTextToSpeech::State state);
    void onStop();
    void onClear();

private:
    bool m_initialized{false};
    QString m_serverName;
    QString m_instName;
    bool m_masterInst{false};
    int m_initInterval{10};
    int m_checkInterval{5};
    QTimer m_initTimer;
    QTimer m_checkTimer;
    QTimer m_triggerTimer;
    int m_currentStep{0};
    std::string m_currentAlarmLevel;
    bool m_isFilePlaying{false};
    bool m_isSpeechPlaying{false};
    QSoundEffect* m_currentSoundEffect{nullptr};
    QTextToSpeech m_textToSpeech;
    std::deque<Voice> m_immediateSpeech;
    std::map<std::string, std::deque<Voice>> m_mapLevelSpeech;
    std::unordered_map<std::string, AlarmLevel> m_mapAlarmLevel;
    std::mutex m_mutex;
    size_t m_maxVoiceSize{1000000};
    int m_maxPlayCount{1000000};
    QString m_voiceFilePath;
    std::string m_alarmLevel0{"ZG_AL_LEVEL0"};
    int m_tickCount{0};
};

#endif // ZGSPVOICEPLAYMNG_H

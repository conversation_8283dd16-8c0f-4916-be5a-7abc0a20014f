#include "ZGSPVoicePlayMng.h"

#include "ZGDebugMng.h"
#include "ZGHeartMng.h"
#include "ZGProxyCommon.h"
#include "ZGPubFun.h"
#include "ZGRuntime.h"
#include "ZGUtils.h"

#include <QDebug>
#include <QFile>
#include <QUrl>
#include <QRandomGenerator>


#pragma comment(lib, "user32.lib")

static ZGSPVoicePlayMng* g_pVoicePlayMng = nullptr;

ZGSPVoicePlayMng* ZGSPVoicePlayMng::instance()
{
    if (g_pVoicePlayMng == nullptr)
        g_pVoicePlayMng = new ZGSPVoicePlayMng;
    return g_pVoicePlayMng;
}

void ZGSPVoicePlayMng::init()
{
    initTextToSpeech();
    initEvents();
    initServerInstConfig();
    start();
    ZGLOG_INFO("ZGSPVoicePlay init start...");
}

bool ZGSPVoicePlayMng::checkState()
{
    return m_initialized;
}

void ZGSPVoicePlayMng::start()
{
    if (!m_masterInst)
        return;
    m_triggerTimer.start();
}

void ZGSPVoicePlayMng::stop()
{
    if (!m_masterInst)
        return;
    emit stoping();
}

void ZGSPVoicePlayMng::clear()
{
    if (!m_masterInst)
        return;
    emit clearing();
}

void ZGSPVoicePlayMng::run()
{
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        msleep(m_initInterval * 1000);
    }
    sleep(QRandomGenerator::global()->bounded(5, 10));
    while (!initAlarmLevelParam())
    {
        ZGLOG_ERROR("initAlarmLevelParam error.");
        msleep(m_initInterval * 1000);
    }
    m_masterInst = ZGRuntime::instance()->isMaster();
    m_initialized = true;
    emit initFinished();
    ZGLOG_INFO("ZGSPVoicePlay init finished.");
}

void ZGSPVoicePlayMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPVoicePlayMng::onCheckStatus);
    connect(&m_triggerTimer, &QTimer::timeout, this, &ZGSPVoicePlayMng::onTrigger);
    connect(&m_textToSpeech, &QTextToSpeech::stateChanged, this, &ZGSPVoicePlayMng::onStateChanged);
    connect(this, &ZGSPVoicePlayMng::stoping, this, &ZGSPVoicePlayMng::onStop);
    connect(this, &ZGSPVoicePlayMng::clearing, this, &ZGSPVoicePlayMng::onClear);
    connect(this, &ZGSPVoicePlayMng::initFinished, this, &ZGSPVoicePlayMng::onInitFinished);
}

void ZGSPVoicePlayMng::initServerInstConfig()
{
    const auto& serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
}

bool ZGSPVoicePlayMng::initServerInstInfo()
{
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

void ZGSPVoicePlayMng::onInitFinished()
{
    m_checkTimer.start(m_checkInterval * 1000);
    m_triggerTimer.start(500);
}

void ZGSPVoicePlayMng::play(const std::string& alarmLevelID, const std::string& speechText)
{
    if (!m_masterInst)
        return;
    playVoice(alarmLevelID, speechText);
}

void ZGSPVoicePlayMng::playMulti(const ZG6000::ListStringMap& listVoice)
{
    if (!m_masterInst)
        return;
    for (const auto& mapVoice : listVoice)
    {
        try
        {
            const auto & alarmLevel = mapVoice.at("alarmLevelID");
            if (alarmLevel == m_alarmLevel0)
                continue;
            const auto & speechText = mapVoice.at("speechText");
            playVoice(alarmLevel, speechText);
        }
        catch (const std::exception& e)
        {
            ZGLOG_ERROR(e.what());
        }
    }
}

void ZGSPVoicePlayMng::speak(const std::string& speechText)
{
    if (!m_masterInst)
        return;
    speakCount(speechText, 1);
}

void ZGSPVoicePlayMng::speakCount(const std::string& speechText, int repeatCount)
{
    if (!m_masterInst)
        return;
    std::unique_lock locker(m_mutex);
    Voice voice;
    voice.alarmLevelID = m_mapAlarmLevel.begin()->first;
    voice.playType = PlayType::ptPlayTTS;
    voice.content = speechText;
    voice.playCount = repeatCount;
    m_immediateSpeech.emplace_back(voice);
}

ZGSPVoicePlayMng::ZGSPVoicePlayMng(QObject *parent) : QThread(parent)
{

}

void ZGSPVoicePlayMng::initTextToSpeech()
{
	m_textToSpeech.setLocale(QLocale::Chinese);
	m_textToSpeech.setRate(0.0);
	m_textToSpeech.setPitch(1.0);
	m_textToSpeech.setVolume(1.0);
	m_voiceFilePath = ZGPubFun::getRootDir() + "/voice/";
}

void ZGSPVoicePlayMng::playVoice(const std::string& alarmLevelID, const std::string& speechText)
{
    try
    {        
        const auto & alarmLevel = m_mapAlarmLevel.at(alarmLevelID);
        auto& voices = m_mapLevelSpeech.at(alarmLevelID);
        Voice voice;
        voice.playCount = alarmLevel.playCount;
        if (alarmLevel.isPlayFile)
        {
            voice.playType = PlayType::ptPlayFile;
            voice.alarmLevelID = alarmLevelID;
            std::unique_lock locker(m_mutex);
            voices.emplace_back(voice);
            if (voices.size() > m_maxVoiceSize)
                voices.pop_front();
        }
        if (alarmLevel.isPlayTTS)
        {
            voice.playType = PlayType::ptPlayTTS;
            voice.content = speechText;
            std::unique_lock locker(m_mutex);
            voices.emplace_back(voice);
            if (voices.size() > m_maxVoiceSize)
                voices.pop_front();
        }
    }
    catch (const std::exception&)
    {
        ZGLOG_ERROR(QString("Can't find alarmLevelID %1").arg(alarmLevelID.c_str()));
    }
}

void ZGSPVoicePlayMng::doPlay(const Voice& voice)
{
    if (m_currentSoundEffect && m_currentSoundEffect->isPlaying())
        m_currentSoundEffect->stop();
    if (m_textToSpeech.state() == QTextToSpeech::Speaking)
        m_textToSpeech.stop();
    m_currentAlarmLevel = voice.alarmLevelID;
    switch (voice.playType)
    {
    case PlayType::ptPlayFile:
        {
            auto pair = m_mapAlarmLevel.find(voice.alarmLevelID);
            if (pair == m_mapAlarmLevel.end())
                break;
            if (!pair->second.soundEffect)
                break;
            m_currentSoundEffect = pair->second.soundEffect;
            pair->second.soundEffect->setLoopCount(voice.playCount);
            pair->second.soundEffect->play();
            m_isFilePlaying = true;
        }
        break;
    case PlayType::ptPlayTTS:
        {
            std::string text{""};
            for (int i = 0; i < voice.playCount; ++i)
            {
                text += voice.content;
            }
            m_textToSpeech.say(text.c_str());
            m_isSpeechPlaying = true;
        }
        break;
    case PlayType::ptNone: 
        break;
    }
}

bool ZGSPVoicePlayMng::getAvailableVoice(Voice& voice)
{
    std::unique_lock locker(m_mutex);
    if (!m_immediateSpeech.empty())
    {
        voice = m_immediateSpeech.front();
        m_immediateSpeech.pop_front();
        return true;
    }
    auto itStart = m_mapLevelSpeech.begin();
    auto itEnd = m_mapLevelSpeech.end();
    if (m_isFilePlaying || m_isSpeechPlaying)
    {
        itEnd = m_mapLevelSpeech.find(m_currentAlarmLevel);
        if (itEnd == itStart)
            return false;
    }
    for (auto it = itStart; it != itEnd; ++it)
    {
        auto& voices = it->second;
        if (voices.empty())
            continue;
        const auto & alarmLevel = it->first;
        const auto & pairLevel = m_mapAlarmLevel.find(alarmLevel);
        if (pairLevel == m_mapAlarmLevel.end())
            continue;
        if (pairLevel->second.isPlayFile && pairLevel->second.isPlayTTS)
        {
            try
            {
                if (voices.at(0).playCount < voices.at(1).playCount)
                {
                    voice = voices.at(1);
                    voice.playCount = 1;
                    --voices.at(1).playCount;
                    if (voices.at(1).playCount == 0)
                    {
                        voices.pop_front();
                        voices.pop_front();
                    }
                }
                else
                {
                    voice = voices.at(0);
                    voice.playCount = 1;
                    --voices.at(0).playCount;
                }
                return true;
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(e.what());
                voices.pop_front();
            }
        }
        else
        {
            try
            {
                voice = voices.at(0);
                voice.playCount = 1;
                --voices.at(0).playCount;
                if (voices.at(0).playCount == 0)
                    voices.pop_front();
                return true;
            }
            catch (const std::exception& e)
            {
                ZGLOG_ERROR(e.what());
            }
        }
    }
    return false;
}

bool ZGSPVoicePlayMng::initAlarmLevelParam()
{
    ZG6000::ListStringMap listRecord;
    if (!ZGProxyCommon::execQuerySql("SELECT * FROM sp_dict_alarm_level", listRecord))
        return false;
    try
    {
        for (const auto& record : listRecord)
        {
            std::string id = record.at("id");
            if (id == m_alarmLevel0)
                continue;
            AlarmLevel alarmLevel;
            alarmLevel.isPlayFile = std::atoi(record.at("isPlayFile").c_str());
            alarmLevel.isPlayTTS = std::atoi(record.at("isPlayTTS").c_str());
            alarmLevel.playCount = std::atoi(record.at("playCount").c_str());
            if (alarmLevel.playCount < 0)
                alarmLevel.playCount = m_maxPlayCount;
            alarmLevel.soundEffect = new QSoundEffect;
            QString voiceFile = m_voiceFilePath + id.c_str() + ".wav";
            if (!QFile::exists(voiceFile))
            {
                ZGLOG_ERROR(QString("voice file '%1' does not exists.").arg(voiceFile));
                return false;
            }
            QThread::msleep(500);
            alarmLevel.soundEffect->setSource(QUrl::fromLocalFile(voiceFile));
            alarmLevel.soundEffect->setVolume(1.0f);
            alarmLevel.soundEffect->setLoopCount(1);

            connect(alarmLevel.soundEffect, &QSoundEffect::playingChanged, this, &ZGSPVoicePlayMng::onPlayingChanged);
            m_mapAlarmLevel.insert(std::make_pair(id, alarmLevel));
            std::deque<Voice> voices;
            m_mapLevelSpeech.insert(std::make_pair(id, voices));
        }
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

void ZGSPVoicePlayMng::onCheckStatus()
{
    m_masterInst = ZGRuntime::instance()->isMaster();
}

void ZGSPVoicePlayMng::onTrigger()
{
    Voice voice;
    if (getAvailableVoice(voice))
    {
        doPlay(voice);
    }
}

void ZGSPVoicePlayMng::onPlayingChanged()
{
    m_isFilePlaying = m_currentSoundEffect->isPlaying();
}

void ZGSPVoicePlayMng::onStateChanged(QTextToSpeech::State state)
{
    m_isSpeechPlaying = state == QTextToSpeech::Speaking;
}

void ZGSPVoicePlayMng::onStop()
{
    m_triggerTimer.stop();
    if (m_isFilePlaying)
    {
        if (m_currentSoundEffect && m_currentSoundEffect->isPlaying())
            m_currentSoundEffect->stop();       
    }
    if (m_isSpeechPlaying)
    {
        if (m_textToSpeech.state() == QTextToSpeech::Speaking)
            m_textToSpeech.stop();
    }
}

void ZGSPVoicePlayMng::onClear()
{
    stop();
    std::unique_lock locker(m_mutex);
    for (auto& pair : m_mapLevelSpeech)
    {
        pair.second.clear();
    }
}

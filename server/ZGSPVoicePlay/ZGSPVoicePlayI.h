#ifndef __ZGSPVoicePlayI_h__
#define __ZGSPVoicePlayI_h__

#include <ZGSPVoicePlay.h>

namespace ZG6000
{

class ZGSPVoicePlayI : public virtual ZGSPVoicePlay
{
public:
    ZGSPVoicePlayI();

    bool checkState(const Ice::Current&) override;

    void play(::std::string,
              ::std::string,
              const Ice::Current&) override;

    void playMulti(ListStringMap,
                   const Ice::Current&) override;

    void speak(::std::string,
               const Ice::Current&) override;

    void speakCount(::std::string,
                    int,
                    const Ice::Current&) override;

    void start(const Ice::Current&) override;

    void stop(const Ice::Current&) override;

    void clear(const Ice::Current&) override;
};

}

#endif

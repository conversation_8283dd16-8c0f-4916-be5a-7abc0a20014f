#include "ZGSPRTSQLServer.h"
#include "ZGDebugMng.h"
#include <QDebug>

#ifdef Q_OS_WIN

ZGSPRTSQLServer::ZGSPRTSQLServer(QObject *parent)
    : ZGSPRTDatabase{parent}
{
    auto ret = SQLAllocHandle(SQL_HANDLE_DBC, m_env.getEnv(), &m_hdbc);
    if (ret != SQL_SUCCESS && ret != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("S<PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON> failed!");
        recordError(SQL_HANDLE_ENV, m_env.getEnv());
    }
}

ZGSPRTSQLServer::~ZGSPRTSQLServer()
{
    this->close();
}

bool ZGSPRTSQLServer::connect()
{
    // 连接数据库
    QString connStr = QString("DRIVER={SQL Server};SERVER=%1;DATABASE=%2;UID=%3;PWD=**;")
                            .arg(m_host)
                            .arg(m_database)
                            .arg(m_userName)
                            .arg(m_password);
    auto ret = SQLDriverConnect(m_hdbc, nullptr, (SQLWCHAR*)connStr.utf16(), SQL_NTS, nullptr, 0, nullptr, SQL_DRIVER_NOPROMPT);
    if (ret != SQL_SUCCESS && ret != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("SQLDriverConnect failed!");
        // 调用SQLGetDiagRec获取错误信息并输出
        recordError(SQL_HANDLE_DBC, m_hdbc);
        return false;
    }
    ret = SQLAllocHandle(SQL_HANDLE_STMT, m_hdbc, &m_hstmt);
    if (ret != SQL_SUCCESS && ret != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("SQLAllocHandle failed!");
        recordError(SQL_HANDLE_DBC, m_hdbc);
        return false;
    }
    // SQLULEN rowsetSize = 100;
    // ret = SQLSetStmtAttr(m_hstmt, SQL_ATTR_ROW_ARRAY_SIZE, (SQLPOINTER)rowsetSize, 0);
    // if (ret != SQL_SUCCESS && ret != SQL_SUCCESS_WITH_INFO)
    // {
    //     ZGLOG_ERROR("SQLSetStmtAttr failed!");
    //     recordError(SQL_HANDLE_STMT, m_hstmt);
    //     return false;
    // }
    // 切换数据库
    QString sql = QString("USE %1").arg(m_database);
    ret = SQLExecDirect(m_hstmt, (SQLWCHAR*)sql.utf16(), SQL_NTS);
    if (ret != SQL_SUCCESS && ret != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("SQLExecDirect failed!");
        recordError(SQL_HANDLE_STMT, m_hstmt);
        return false;
    }
    return true;
}

void ZGSPRTSQLServer::close()
{
    // 释放语句句柄
    SQLFreeHandle(SQL_HANDLE_STMT, m_hstmt);
    // 断开连接
    SQLDisconnect(m_hdbc);
    // 释放连接句柄
    SQLFreeHandle(SQL_HANDLE_DBC, m_hdbc);
}

bool ZGSPRTSQLServer::execQuery(const QString& sql, const FuncRow& processRow)
{
    SQLFreeStmt(m_hstmt, SQL_CLOSE);
    auto retcode = SQLExecDirect(m_hstmt, (SQLWCHAR*)sql.utf16(), SQL_NTS);
    if (retcode != SQL_SUCCESS && retcode != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("SQLExecDirect failed!");
        return false;
    }
    SQLSMALLINT columnCount;
    retcode = SQLNumResultCols(m_hstmt, &columnCount);
    if (retcode != SQL_SUCCESS && retcode != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("SQLNumResultCols failed!");
        recordError(SQL_HANDLE_STMT, m_hstmt);
        return false;
    }
    // 获取列信息
    std::vector<wchar_t*> buffers;
    std::vector<QByteArray> strBuffers;
    std::vector<char*> utf8Buffers;
    std::vector<SQLSMALLINT> dataTypes;
    std::vector<SQLULEN> bufferLengths;
    SQLSMALLINT nameLen;
    SQLSMALLINT dataType;
    SQLULEN columnSize;
    SQLSMALLINT decimalDigits;
    SQLSMALLINT nullable;
    for (int i = 1; i <= columnCount; i++)
    {
        SQLWCHAR columnName[256];
        retcode = SQLDescribeColW(m_hstmt, i, columnName, sizeof(columnName), &nameLen, &dataType, &columnSize, &decimalDigits, &nullable);
        if (retcode != SQL_SUCCESS && retcode != SQL_SUCCESS_WITH_INFO)
        {
            ZGLOG_ERROR("SQLDescribeColW failed!");
            recordError(SQL_HANDLE_STMT, m_hstmt);
            return false;
        }
        if (columnSize > 10 * 1024 * 1024)
            columnSize = 10 * 1024 * 1024; // 限制列大小为10MB
        buffers.push_back(new wchar_t[columnSize + 1]);
        dataTypes.push_back(dataType);
        bufferLengths.push_back((columnSize + 1) * sizeof(wchar_t));
        strBuffers.emplace_back();
        utf8Buffers.push_back(nullptr);
    }
    // 获取数据
    while (SQLFetch(m_hstmt) != SQL_NO_DATA)
    {
        for (int i = 1; i <= columnCount; i++)
        {
            SQLLEN length;
            auto dataType = dataTypes[i - 1];
            long value;
            if (dataType == SQL_INTEGER)
            {
                retcode = SQLGetData(m_hstmt, i, SQL_C_LONG, &value, sizeof(value), &length);
                if (retcode != SQL_SUCCESS && retcode != SQL_SUCCESS_WITH_INFO)
                {
                    ZGLOG_ERROR("SQLGetData failed!");
                    recordError(SQL_HANDLE_STMT, m_hstmt);
                    return false;
                }
                QString strValue;
                if (length == SQL_NULL_DATA)
                    strValue = "";
                else
                    strValue = QString::number(value);
                strBuffers[i - 1] = strValue.toLatin1();
                utf8Buffers[i - 1] = strBuffers[i - 1].data();
            }
            else
            {
                retcode = SQLGetData(m_hstmt, i, SQL_C_WCHAR, buffers[i - 1], bufferLengths[i - 1], &length);
                if (retcode != SQL_SUCCESS && retcode != SQL_SUCCESS_WITH_INFO)
                {
                    ZGLOG_ERROR("SQLGetData failed!");
                    recordError(SQL_HANDLE_STMT, m_hstmt);
                    return false;
                }
                if (length == SQL_NULL_DATA)
                    buffers[i - 1][0] = L'\0';
                else
                    buffers[i - 1][length / sizeof(wchar_t)] = L'\0';
                QStringView bufferView(buffers[i - 1]);
                strBuffers[i - 1] = bufferView.toUtf8();
                utf8Buffers[i - 1] = strBuffers[i - 1].data();
            }
        }
        char** p = utf8Buffers.data();
        if (processRow)
            processRow(columnCount, p);
    }
    for (auto buffer : buffers)
        delete[] buffer;
    return true;
}

bool ZGSPRTSQLServer::execCommand(const QString& sql, const FuncResult& processResult)
{
    SQLFreeStmt(m_hstmt, SQL_CLOSE);
    auto retcode = SQLExecDirect(m_hstmt, (SQLWCHAR*)sql.utf16(), SQL_NTS);
    if (retcode != SQL_SUCCESS && retcode != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("SQLExecDirect failed!");
        recordError(SQL_HANDLE_STMT, m_hstmt);
        return false;
    }
    SQLLEN affectedRows;
    retcode = SQLRowCount(m_hstmt, &affectedRows);
    if (retcode != SQL_SUCCESS && retcode != SQL_SUCCESS_WITH_INFO)
    {
        ZGLOG_ERROR("SQLRowCount failed!");
        recordError(SQL_HANDLE_STMT, m_hstmt);
        return false;
    }
    if (processResult)
        processResult(affectedRows);
    return true;
}

bool ZGSPRTSQLServer::getTables(std::vector<std::string>& tables)
{
    // 获取当前数据库中所有的用户表名称
    QString sql = QString("SELECT name FROM sys.tables WHERE is_ms_shipped = 0 ORDER BY name");
    return execQuery(sql, [&tables](size_t num_fields, char** row)
                     {
                         tables.push_back(row[0]);
                     });
}

bool ZGSPRTSQLServer::getTableFields(const std::string& tableName, std::vector<std::string>& fieldsName)
{
    // 获取指定表名的所有字段
    QString sql = QString("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '%1'").arg(tableName.c_str());
    return execQuery(sql, [&fieldsName](size_t num_fields, char** row)
                     {
                         fieldsName.push_back(row[0]);
                     });
}

bool ZGSPRTSQLServer::getTableFields(const QString& tableName, QStringList& fieldsName)
{
    QString sql = QString("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '%1'").arg(tableName);
    return execQuery(sql, [&fieldsName](size_t num_fields, char** row)
                     {
                         fieldsName.push_back(row[0]);
                     });
}

bool ZGSPRTSQLServer::getTableFields(const std::string& tableName, std::map<std::string, std::string>& fields)
{
    // 得到指定表名的所有字段及其类型
    QString sql = QString("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '%1'").arg(tableName.c_str());
    return execQuery(sql, [&fields](size_t num_fields, char** row)
                     {
                         fields[row[0]] = row[1];
                     });
}

void ZGSPRTSQLServer::recordError(SQLSMALLINT handleType, SQLHANDLE handle)
{
    // 记录错误信息
    SQLSMALLINT iRec = 0;
    SQLRETURN retcode;
    SQLINTEGER nativeError;
    SQLWCHAR szErrorMsg[SQL_MAX_MESSAGE_LENGTH];
    SQLSMALLINT cbErrorMsg;
    SQLGetDiagFieldW(handleType, handle, 0, SQL_DIAG_NUMBER, &iRec, SQL_IS_INTEGER, nullptr);
    for (SQLSMALLINT i = 1; i <= iRec; i++)
    {
        SQLWCHAR szSqlState[6];
        retcode = SQLGetDiagRecW(handleType, handle, i, szSqlState, &nativeError, (SQLWCHAR*)szErrorMsg, SQL_MAX_MESSAGE_LENGTH, &cbErrorMsg);
        if (retcode == SQL_SUCCESS || retcode == SQL_SUCCESS_WITH_INFO)
        {
            QString state = QString::fromWCharArray(szSqlState);
            QString msg = QString::fromWCharArray(szErrorMsg);
            ZGLOG_ERROR(QString("SQLSTATE: %1").arg(state));
            ZGLOG_ERROR(QString("Message: %1").arg(msg));
        }
    }
}

ZGSPRTSQLServerEnvironment::ZGSPRTSQLServerEnvironment()
{
    // 初始化环境句柄
    SQLAllocHandle(SQL_HANDLE_ENV, SQL_NULL_HANDLE, &m_hEnv);
    // 设置环境句柄的属性
    SQLSetEnvAttr(m_hEnv, SQL_ATTR_ODBC_VERSION, (SQLPOINTER)SQL_OV_ODBC3, 0);
}

ZGSPRTSQLServerEnvironment::~ZGSPRTSQLServerEnvironment()
{
    // 释放环境句柄
    SQLFreeHandle(SQL_HANDLE_ENV, m_hEnv);
}

SQLHENV ZGSPRTSQLServerEnvironment::getEnv() const
{
    return m_hEnv;
}

#endif

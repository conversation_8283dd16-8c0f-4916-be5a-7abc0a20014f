#ifndef ZGSPRTDATAMNG_H
#define ZGSPRTDATAMNG_H

#include <QThread>
#include <QString>
#include <QList>
#include <QTimer>
#include <Ice/Ice.h>
#include <shared_mutex>
#include <QReadWriteLock>
#include "ZGServerCommon.h"
#include "ZGSPRTDatabase.h"
#include "ZGRuntime.h"
#include "redis/redis.h"
#include "ObjectPool.h"

struct DatabaseParams
{
    QString name;
    QString addr;
    int port{3306};
    QString user;
    QString password;
    int timeout{10};
};

struct RedisParams
{
    QString addr;
    int port;
};

struct redisContext;
struct redisReply;
class ZGRuntime;
class ZGRedisClient;
class ZGSPRTDataMng : public QObject
{
    Q_OBJECT
public:
    static ZGSPRTDataMng* instance();
    void init();
public:
    bool checkState();

    bool initialized();

    virtual bool getData(const ::std::string& key,
                         ::std::string& value, ZG6000::ErrorInfo& e);

    virtual bool setData(const ::std::string& key,
                         const ::std::string& value, ZG6000::ErrorInfo& e);

    virtual bool getDataBatch(const ::ZG6000::StringList& listKey,
                              ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e);

    virtual bool setDataBatch(const ::ZG6000::StringList& listKey,
                              const ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e);

    virtual bool getDataBatchToMap(const ::ZG6000::StringList& listKey,
                                   ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e);

    virtual bool setDataBatchMap(const ::ZG6000::StringMap& keyValue, ZG6000::ErrorInfo& e);

    virtual bool getDataByKeyToValue(const ::std::string& key,
                              ::std::string& value, ZG6000::ErrorInfo& e);

    virtual bool getDataByKeyToJson(const ::std::string& key,
                                    ::std::string& jsonValue, ZG6000::ErrorInfo& e);

    virtual bool setDataByKeyFromValue(const ::std::string& key,
                              const ::std::string& value, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByKeyToList(const ::ZG6000::StringList& listKey,
                               ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByKeyToMap(const ::ZG6000::StringList& listKey,
                                    ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByKeyToJson(const ::ZG6000::StringList& listKey,
                                     ::std::string& jsonValue, ZG6000::ErrorInfo& e);

    virtual bool msetDataByKeyFromList(const ::ZG6000::StringList& listKey,
                               const ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e);

    virtual bool msetDataByKeyFromMap(const ::ZG6000::StringMap& keyValue, ZG6000::ErrorInfo& e);
    
    virtual bool getDataByIDToMap(const ::std::string& tableName,
                                  const ::std::string& id,
                                  ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e);

    virtual bool getDataByIDToJson(const ::std::string& tableName,
                                   const ::std::string& id,
                                   ::std::string& jsonValue, ZG6000::ErrorInfo& e);

    virtual bool getDataByFieldToValue(const ::std::string& tableName,
                                const ::std::string& id,
                                const ::std::string& fieldName,
                                ::std::string& fieldValue, ZG6000::ErrorInfo& e);

    virtual bool getDataByFieldToJson(const ::std::string& tableName,
                                      const ::std::string& id,
                                      const ::std::string& fieldName,
                                      ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e);

    virtual bool getDataByFieldsToList(const ::std::string& tableName,
                                 const ::std::string& id,
                                 const ::ZG6000::StringList& listFieldName,
                                 ::ZG6000::StringList& listFieldValue, ZG6000::ErrorInfo& e);

    virtual bool getDataByFieldsToMap(::std::string tableName,
                                        ::std::string id,
                                        ZG6000::StringList listFieldName,
                                        ZG6000::StringMap& mapFieldValue,
                                        ZG6000::ErrorInfo& e);


    virtual bool getDataByFieldsToJson(const ::std::string& tableName,
                                       const ::std::string& id,
                                       const ::ZG6000::StringList& listFieldName,
                                       ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByIDToListList(const ::std::string& tableName,
                               const ::ZG6000::StringList& listID,
                               ::ZG6000::ListStringList& listFieldValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByIDToJson(const ::std::string& tableName,
                                     const ::ZG6000::StringList& listID,
                                     ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByIDToListMap(::std::string tableName,
                                  ZG6000::StringList listID,
                                  ZG6000::ListStringMap& listFieldMapValue,
                                  ZG6000::ErrorInfo& e);

    virtual bool mgetDataByIDToMapMap(::std::string tableName,
                                     ZG6000::StringList listID,
                                     ZG6000::MapStringMap& mapFieldValue,
                                     ZG6000::ErrorInfo& e);

    virtual bool mgetDataByFieldToList(const ::std::string& tableName,
                                 const ::ZG6000::StringList& listID,
                                 const ::std::string& fieldName,
                                 ::ZG6000::StringList& listFieldValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByFieldToMap(::std::string tableName,
                                     ZG6000::StringList listID,
                                     ::std::string fieldName,
                                     ZG6000::StringMap& mapValue,
                                     ZG6000::ErrorInfo& e);

    virtual bool mgetDataByFieldToJson(const ::std::string& tableName,
                                       const ::ZG6000::StringList& listID,
                                       const ::std::string& fieldName,
                                       ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByFieldsToListList(const ::std::string& tableName,
                                  const ::ZG6000::StringList& listID,
                                  const ::ZG6000::StringList& listFieldName,
                                  ::ZG6000::ListStringList& listFieldValue, ZG6000::ErrorInfo& e);

    virtual bool mgetDataByFieldsToJson(const ::std::string& tableName,
                                        const ::ZG6000::StringList& listID,
                                        const ::ZG6000::StringList& listFieldName,
                                        ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e);
    
    virtual bool mgetDataByFieldsToListMap(::std::string tableName,
                                     ZG6000::StringList listID,
                                     ZG6000::StringList listFieldName,
                                     ZG6000::ListStringMap& listFieldMapValue,
                                     ZG6000::ErrorInfo& e);

    virtual bool mgetDataByFieldsToMapMap(::std::string tableName,
                                        ZG6000::StringList listID,
                                            ZG6000::StringList listFieldName,
                                            ZG6000::MapStringMap& mapFieldValue,
                                            ZG6000::ErrorInfo& e);

    virtual bool updateDataByFieldFromValue(const ::std::string& tableName,
                                   const ::std::string& id,
                                   const ::std::string& fieldName,
                                   const ::std::string& fieldValue, ZG6000::ErrorInfo& e);

    virtual bool mupdateDataByFieldFromValue(::std::string tableName,
                                        ZG6000::StringList listID,
                                            ::std::string fieldName,
                                            ::std::string fieldValue,
                                            ZG6000::ErrorInfo& e);

    /** 
     * 更新单条数据
     * @param tableName 表名
     * @param id 数据ID
     * @param mapValue 更新数据
     * @param e 执行出错时的错误描述
     * @return 执行成功返回true，失败返回false
     */
    virtual bool updateDataByIDFromMap(const ::std::string& tableName,
                                const ::std::string& id,
                                const ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e);

    virtual bool mupdateDataByFieldFromList(const ::std::string& tableName,
                                    const ::ZG6000::StringList& listID,
                                    const ::std::string& fieldName,
                                    const ::ZG6000::StringList& listFieldValue, ZG6000::ErrorInfo& e);

    virtual bool mupdateValueByFieldsFromListList(const ::std::string& tableName,
                                         const ::ZG6000::StringList& listID,
                                         const ::ZG6000::StringList& listFieldName,
                                         const ::ZG6000::ListStringList& listFieldValue, ZG6000::ErrorInfo& e);
    
    virtual bool mupdateDataByFieldsFromListMap(const ::std::string& tableName,
                                     const ::ZG6000::StringList& listID,
                                     const ::ZG6000::ListStringMap& listFieldMapValue,
                                     ZG6000::ErrorInfo& e);

    virtual bool mupdateDataByFieldsFromMap(::std::string tableName,
                                         ZG6000::StringList listID,
                                             ZG6000::StringMap fieldMapValue,
                                             ZG6000::ErrorInfo& e);

    virtual bool updateDataID(::std::string tableName,
                                  ::std::string oldID,
                                  ::std::string newID,
                                  ZG6000::ErrorInfo& e);

    virtual bool mupdateDataID(::std::string tableName,
                               ZG6000::StringList listOldID,
                               ZG6000::StringList listNewID,
                                   ZG6000::ErrorInfo& e);

    virtual bool insertDataByFieldFromValue(const ::std::string& tableName,
                                   const ::std::string& id,
                                   const ::std::string& fieldName,
                                   const ::std::string& fieldValue, ZG6000::ErrorInfo& e);

    virtual bool insertDataByIDFromMap(const ::std::string& tableName,
                                const ::std::string& id,
                                const ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e);

    virtual bool minsertDataByFieldFromList(const ::std::string&,
                                    const ::ZG6000::StringList&,
                                    const ::std::string&,
                                    const ::ZG6000::StringList&, ZG6000::ErrorInfo& e);

    virtual bool minsertDataByFieldsFromListMap(const ::std::string& tableName,
                                     const ::ZG6000::StringList& listID,
                                     const ::ZG6000::ListStringMap& listFieldMapValue, ZG6000::ErrorInfo& e);

    virtual bool deleteDataByID(const ::std::string& tableName,
                                const ::std::string& id, ZG6000::ErrorInfo& e);

    virtual bool mdeleteDataByID(const ::std::string& tableName,
                                  const ::ZG6000::StringList& listID, ZG6000::ErrorInfo& e);

    virtual bool deleteDataByField(const ::std::string& tableName,
                                     const ::std::string& id,
                                     const ::std::string& fieldName, ZG6000::ErrorInfo& e);

    virtual bool deleteDataByFields(const ::std::string& tableName,
                                      const ::std::string& id,
                                      const ::ZG6000::StringList& listFieldName, ZG6000::ErrorInfo& e);

    virtual bool mdeleteDataByField(const ::std::string& tableName,
                                      const ::ZG6000::StringList& listID,
                                      const ::std::string& fieldName, ZG6000::ErrorInfo& e);

    virtual bool mdeleteDataByFields(const ::std::string& tableName,
                                     const ::ZG6000::StringList& listID,
                                     const ::ZG6000::StringList& listFieldName, ZG6000::ErrorInfo& e);

    virtual bool test(const Ice::Current&);

    virtual bool get();

protected:
    using ReplyFunc = std::function<bool(redisReply*)>;
    bool getDataByIDReply(const ::std::string& tableName,
                             const ::std::string& id,
                             ZG6000::ErrorInfo& e,
                            const ReplyFunc& func);

    bool getDataByFieldReply(const ::std::string& tableName,
                             const ::std::string& id,
                             const ::std::string& fieldName,
                             ::std::string& fieldValue,
                             ZG6000::ErrorInfo& e,
                             const ReplyFunc& func);

    bool getDataByFieldsReply(const ::std::string& tableName,
                                 const ::std::string& id,
                                 const ::ZG6000::StringList& listFieldName,
                                 ZG6000::ErrorInfo& e,
                                 const ReplyFunc& func);

    using ContextFunc = std::function<bool(redisContext* context)>;
    bool mgetDataByFieldReply(const ::std::string& tableName,
                              const ::ZG6000::StringList& listID,
                              const ::std::string& fieldName,
                              ZG6000::ErrorInfo& e,
                              const ContextFunc& func);

    bool mgetDataByIDsReply(const ::std::string& tableName,
                            const ::ZG6000::StringList& listID,
                            ZG6000::ErrorInfo& e,
                            const std::function<bool(const ::ZG6000::StringList& fieldNames)>& func);

    bool mgetDataByFieldsReply(const ::std::string& tableName,
                                  const ::ZG6000::StringList& listID,
                                  const ::ZG6000::StringList& listFieldName,
                                  ZG6000::ErrorInfo& e,
                                  const ContextFunc& func);
    
    void onCheckTable();

private:
    explicit ZGSPRTDataMng(QObject *parent = nullptr);
    void initEvents();
    bool initServerInstInfo();
    void initServerInstConfig();
    bool initDataSync();
    bool tryConnectDatabase();
    bool tryCreateRedisClient();
    bool getRedisTopicClient();
    bool isClientConnected();
    bool initDatabaseParams();
    bool isDefaultMaster();
    bool checkMasterOnline();
    bool isFirstStart(bool& firstStart);
    bool flushData();
    bool setCacheFlag();
    bool syncTables();
    bool syncFields();
    bool syncData();
    bool syncTableFields(ZGSPRTDatabase& database, const std::string& tableName, ZG6000::StringList&
                                                                                     fieldsName);
    bool importFromTable(const std::string& tableName);
    static bool checkContext(redisContext* context, ZG6000::ErrorInfo& e);
    static bool checkReply(redisReply* reply, ZG6000::ErrorInfo& e);
    bool _check_ctx(redisContext* ctx);
    bool _check_reply(redisReply* reply);
    bool getCachedTableFields(const std::string tableName, ZG6000::StringList& fields, ZG6000::ErrorInfo& e);
    bool getCachedTableFields(const std::string tableName, ZG6000::StringMap& fields, ZG6000::ErrorInfo& e);
    static QString getMapValue(const QMap<QString, QString>& mapParam, const QString& key, QString defaultValue = "");
    static void getMapListKeys(const ZG6000::ListStringMap& listFieldMapValue, std::vector<std::vector<const char*>>& listFieldNames, std::vector<std::vector<const char*>>& listFieldValues);
    static void splitMap(const ZG6000::StringMap& keyValue, std::vector<const char*>& keys, std::vector<const char*>& values);
    static bool appendMultiCommand(redisContext* context, const std::string& tableName, const std::string& id, const ZG6000::StringMap& fieldMapValue);
    static bool appendMultiCommand(redisContext* context, const std::string& tableName, const std::string& id, std::vector<const char* >& commandsGet, std::vector<const char* >& commandsSet, const ZG6000::StringList& fieldValues);
    static bool appendMultiCommand(redisContext* context, const std::string& tableName, const std::string& id, const std::string& fieldName, const std::string& fieldValue);
    static bool verifyQueuedCommand(redisContext* context, size_t commandNum, ZG6000::ErrorInfo& e);
    static void commitMultiCommand(redisContext* ctx, int &commandNum);
    using ContextReplyFunc = std::function<bool(redisContext* context, redisReply* reply)>;
    bool execRedisTransaction(const ContextFunc& contextFunc, const ContextReplyFunc& replyFunc, ZG6000::ErrorInfo& e);
    bool execRedisCommand(std::function<bool(redisContext*)> func, ZG6000::ErrorInfo& e, bool master = false);
    static redisReply* doSendRedisCommand(ZG6000::ErrorInfo& e, redisContext *c, int argc, const char **argv, const size_t *argvlen);
    static redisReply* doSendRedisCommand(ZG6000::ErrorInfo& e, redisContext* c, const char* format, ...);
    static bool doAppendRedisCommand(ZG6000::ErrorInfo& e, redisContext *c, int argc, const char **argv, const size_t *argvlen);
    static bool doAppendRedisCommand(ZG6000::ErrorInfo& e, redisContext *c, const char *format, ...);
    static redisReply* doGetRedisReply(ZG6000::ErrorInfo& e, redisContext *c);
    static bool multiReplyToStringList(redisContext* context, size_t replyNum, ZG6000::StringList& stringList, ZG6000::ErrorInfo& e);
    static bool replyArrayToStringList(redisReply* reply, ZG6000::StringList& stringList, ZG6000::ErrorInfo& e);
    void addTableFields(const std::string& tableName, const ZG6000::StringMap& fields);
    ZGRuntime::DB_TYPE getDatabaseType();
private:
    QString m_serverName;
    QString m_instName;
    QString m_localNodeID;
    std::atomic_bool m_initialized{false};
    std::vector<ConnectionOption> m_connectionOptions;
    std::shared_ptr<Redis> m_pClient{nullptr};
    ZGSPRTDatabase* m_database;
    ObjectPool<ZGSPRTDatabase, QObject*> m_databasePool;
    QList<DatabaseParams> m_listDbParams;
    QList<RedisParams> m_listRtParams;
    ZG6000::MapStringMap m_tablesFields;
    ZGRedisClient* m_pRedisClient{nullptr};
    ZGRedisClient* m_pRedisLocal{nullptr};
    QTimer m_checkTimer;
    int m_selectedDb{0};
    bool m_isMasterServer{false};
    bool m_isStrictMode{false};
    int m_initInterval{10};
    int m_checkInterval{60};
    size_t m_maxRtRedisConn{16};
    size_t m_maxTopicRedisConn{8};
    size_t m_maxRecordNum{1000};
    size_t m_initSlaveCount{0};
    // QReadWriteLock m_lock;
    mutable std::shared_mutex m_mutex;
};

#endif // ZGSPRTDATAMNG_H

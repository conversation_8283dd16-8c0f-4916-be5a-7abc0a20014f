#include "ZGSPRTMySQL.h"
#include <QDebug>

ZGSPRTMySQL::ZGSPRTMySQL(QObject *parent)
    : ZGSPRTDatabase{parent}
{
    mysql_init(&m_mysql);
}

ZGSPRTMySQL::~ZGSPRTMySQL()
{
    close();
}

bool ZGSPRTMySQL::connect()
{
    mysql_options(&m_mysql, MYSQL_OPT_CONNECT_TIMEOUT, reinterpret_cast<const char*>(&m_timeout));
    if (!mysql_real_connect(&m_mysql, m_host.toLatin1().data(), m_userName.toLatin1().data(), m_password.toLatin1().data(),
                            m_database.toLatin1().data(), m_port, nullptr, 0))
    {
        qDebug() << (mysql_error(&m_mysql));
        return false;
    }
    return true;
}

void ZGSPRTMySQL::close()
{
    mysql_close(&m_mysql);
}

bool ZGSPRTMySQL::execQuery(const QString& sql, const FuncRow& processRow)
{
    QByteArray ba = sql.toUtf8();
    const auto& strSql = ba.data();
    if (mysql_real_query(&m_mysql, strSql, static_cast<unsigned long>(sql.length())) != 0)
    {
        qDebug() << (mysql_error(&m_mysql));
        return false;
    }
    MYSQL_RES* result = mysql_store_result(&m_mysql);
    int num_fields = mysql_num_fields(result);
    if (result)
    {
        while (MYSQL_ROW row = mysql_fetch_row(result))
        {
            if (processRow)
                processRow(num_fields, row);
        }
        mysql_free_result(result);
    }
    else
    {
        if(mysql_field_count(&m_mysql) != 0)
        {
            qDebug() << (mysql_error(&m_mysql));
            return false;
        }
    }
    return true;
}

bool ZGSPRTMySQL::execCommand(const QString& sql, const FuncResult& processResult)
{
    QByteArray ba = sql.toUtf8();
    if (mysql_real_query(&m_mysql, ba.data(), static_cast<unsigned long>(sql.length())) != 0)
    {
        qDebug() << (mysql_error(&m_mysql));
        return false;
    }
    MYSQL_RES* result = mysql_store_result(&m_mysql);
    if (!result)
    {
        if(mysql_field_count(&m_mysql) != 0)
        {
            qDebug() << (mysql_error(&m_mysql));
            return false;
        }
        unsigned long long affectedRows = mysql_affected_rows(&m_mysql);
        if (processResult)
            processResult(affectedRows);
    }
    else
        mysql_free_result(result);
    return true;
}

bool ZGSPRTMySQL::getTables(std::vector<std::string>& tables)
{
    QString sql = QString("select table_name from information_schema.tables "
                          "where table_schema='%1' AND TABLE_TYPE = 'BASE TABLE'")
                      .arg(m_database);
    return execQuery(sql, [&tables](size_t num_fields, MYSQL_ROW& row)
                     {
                         std::string tableName = row[0];
                         if (tableName != "sp_maintain_records")
                             tables.push_back(tableName);
                     });
}

bool ZGSPRTMySQL::getTableFields(const std::string& tableName, std::vector<std::string>& fieldsName)
{
    QString sql = QString("SHOW COLUMNS FROM %1").arg(tableName.c_str());
    return execQuery(sql, [&fieldsName](size_t num_fields, MYSQL_ROW& row)
                     {
                         fieldsName.push_back(row[0]);
                     });
}

bool ZGSPRTMySQL::getTableFields(const QString& tableName, QStringList& fieldsName)
{
    QString sql = QString("SHOW COLUMNS FROM %1").arg(tableName);
    return execQuery(sql, [&fieldsName](size_t num_fields, MYSQL_ROW& row)
                     {
                         fieldsName.push_back(row[0]);
                     });
}

bool ZGSPRTMySQL::getTableFields(const std::string &tableName, std::map<std::string, std::string> &fields)
{
    QString sql = QString("SHOW COLUMNS FROM %1").arg(tableName.c_str());
    return execQuery(sql, [&fields](size_t num_fields, MYSQL_ROW& row)
                     {
                         if (row[4])
                             fields[row[0]] = row[4];
                         else
                             fields[row[0]] = "";
                     });
}

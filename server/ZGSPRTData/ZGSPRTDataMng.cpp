#include "ZGSPRTDataMng.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include <chrono>
#include <map>
#include <QtConcurrent>
#include <QDateTime>
#include "ZGPubFun.h"
#include "ZGDebugMng.h"
#include "ZGSPRTDatabase.h"
#include "ZGSPRTMySQL.h"
#include "ZGSPRTSQLServer.h"
#include "zgerror/ZGSPRTDataError.h"
#include "ZGUtils.h"

using namespace rapidjson;

static ZGSPRTDataMng* g_pRTDataServerMng = nullptr;
const int MaxFieldSize = 200;

ZGSPRTDataMng *ZGSPRTDataMng::instance()
{
    if (g_pRTDataServerMng == nullptr)
        g_pRTDataServerMng = new ZGSPRTDataMng;
    return g_pRTDataServerMng;
}

void ZGSPRTDataMng::initEvents()
{
    connect(&m_checkTimer, &QTimer::timeout, this, &ZGSPRTDataMng::onCheckTable);
}

bool ZGSPRTDataMng::initServerInstInfo()
{
    m_localNodeID = ZGPubFun::getLocalNodeID();
    if (m_localNodeID.isEmpty())
    {
        ZGLOG_ERROR("Empty local node ID.");
        return false;
    }
    m_serverName = ZGRuntime::instance()->getServerID();
    if (m_serverName.isEmpty())
    {
        ZGLOG_ERROR("Empty server id.");
        return false;
    }
    m_instName = ZGRuntime::instance()->getInstanceID();
    if (m_instName.isEmpty())
    {
        ZGLOG_ERROR("Empty server instance id.");
        return false;
    }
    return true;
}

void ZGSPRTDataMng::initServerInstConfig()
{
    const auto & serverGroup = ZGRuntime::instance()->getServerConfig("server");
    QString errMsg;
    int value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "init_interval", value, 5, 10, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_initInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "check_interval", value, 10, 60, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_checkInterval = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "max_record_num", value, 100, 1000, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_maxRecordNum = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "max_rt_redis_conn", value, 8, 16, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_maxRtRedisConn = value;
    if (!ZGUtils::getMapKeyValue(serverGroup, "max_topic_redis_conn", value, 8, 16, errMsg))
        ZGLOG_WARN(errMsg);
    else
        m_maxTopicRedisConn = value;
}

bool ZGSPRTDataMng::initDataSync()
{
    while (!m_pRedisLocal->connected())
    {
        QThread::msleep(100);
    }
    ZGLOG_INFO("client connected.");
    std::string deployModeID;
    QString sql = "SELECT deployModeID FROM sp_param_system";
    if (!m_database->execQuery(sql, [&](size_t num_fields, SQL_ROW& row)
    {
        deployModeID = row[0];
    }))
    {
        ZGLOG_ERROR(QString("execQuery error, sql = '%1'").arg(sql));
        return false;
    }
    ZGLOG_INFO(QString("deployMode = %1").arg(deployModeID.c_str()));
    if (!syncFields())
    {
        ZGLOG_ERROR("syncFields error.");
        return false;
    }
    if (deployModeID == "ZG_DM_MASTER_SLAVE")
    {
        if (!syncData())
            return false;
    }
    else
    {
        std::string slaveOf;
        std::string errMsg;
        if (!m_pRedisLocal->config_get("slaveof", slaveOf, errMsg))
        {
            ZGLOG_ERROR(errMsg.c_str());
            return false;
        }
        ZGLOG_INFO(QString("slaveof: %1").arg(slaveOf.c_str()));
        if (slaveOf.empty())
        {
            if (!syncData())
                return false;
        }
        else
        {
            if (m_initSlaveCount < 2)
            {
                ++m_initSlaveCount;
                return false;
            }
        }
    }
    return true;
}

void ZGSPRTDataMng::init()
{
    ZGLOG_INFO("ZGSPRTData init start...");
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        m_database = new ZGSPRTMySQL();
    else if (ZGRuntime::instance()->getDBType() == ZGRuntime::QODBC)
        m_database = new ZGSPRTSQLServer();
    initServerInstConfig();
    initEvents();
    while (!initServerInstInfo())
    {
        ZGLOG_ERROR("initServerInstInfo error.");
        QThread::sleep(m_initInterval);
    }
    ZGLOG_INFO("initDatabaseParams.");
    while (!initDatabaseParams())
    {
        ZGLOG_ERROR("initDatabaseParams error.");
        QThread::sleep(m_initInterval);
    }
    ZGLOG_INFO("tryConnectDatabase.");
    while (!tryConnectDatabase())
    {
        ZGLOG_ERROR("tryConnectDatabase error.");
        QThread::sleep(m_initInterval);
    }
    ZGLOG_INFO("tryCreateRedisClient.");
    while (!tryCreateRedisClient())
    {
        ZGLOG_ERROR("tryCreateRedisClient error.");
        QThread::sleep(m_initInterval);
    }
    ZGLOG_INFO("initDataSync.");
    while (!initDataSync())
    {
        ZGLOG_ERROR("initDataSync error.");
        QThread::sleep(5);
    }
    ZGLOG_INFO("getRedisTopicClient.");
    while (!getRedisTopicClient())
    {
        ZGLOG_ERROR("getRedisTopicClient error.");
        QThread::sleep(m_initInterval);
    }
    m_initialized = true;
    m_checkTimer.start(m_checkInterval * 1000);
    ZGLOG_INFO("ZGSPRTData init finished.");
}

bool ZGSPRTDataMng::initialized()
{
    return m_initialized;
}

bool ZGSPRTDataMng::checkState()
{
    return m_initialized;
}

bool ZGSPRTDataMng::getData(const ::std::string& key, ::std::string& value, ZG6000::ErrorInfo& e)
{
//    auto connection = m_pClient->connection();
    return true;
}

bool ZGSPRTDataMng::setData(const ::std::string& key, const ::std::string& value, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::getDataBatch(const ::ZG6000::StringList& listKey, ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::setDataBatch(const ::ZG6000::StringList& listKey, const ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::getDataBatchToMap(const ::ZG6000::StringList& listKey, ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::setDataBatchMap(const ::ZG6000::StringMap& keyValue, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::getDataByKeyToValue(const ::std::string& key, ::std::string& value, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    size_t firstPos = key.find_first_of("/");
    if (firstPos == std::string::npos)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = u8"key'" + key + u8"'的格式不正确";
        ZGLOG_ERROR(e);
        return false;
    }
    std::string tableName = key.substr(0, firstPos);
    size_t lastPos = key.find_last_of("$");
    if (lastPos == std::string::npos)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = u8"key'" + key + u8"'的格式不正确";
        ZGLOG_ERROR(e);
        return false;
    }
    std::string fieldName = key.substr(lastPos + 1);
    std::string id = key.substr(firstPos + 1, lastPos - firstPos - 1);
    return getDataByFieldToValue(tableName, id, fieldName, value, e);
}

bool ZGSPRTDataMng::getDataByKeyToJson(const ::std::string& key, ::std::string& jsonValue, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::setDataByKeyFromValue(const ::std::string& key, const ::std::string& value, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    size_t firstPos = key.find_first_of("/");
    if (firstPos == std::string::npos)
        return false;
    std::string tableName = key.substr(0, firstPos);
    size_t lastPos = key.find_last_of("$");
    if (lastPos == std::string::npos)
        return false;
    std::string fieldName = key.substr(lastPos + 1);
    std::string id = key.substr(firstPos + 1, lastPos - firstPos - 1);
    return updateDataByFieldFromValue(tableName, id, fieldName, value, e);
}

bool ZGSPRTDataMng::mgetDataByKeyToList(const ::ZG6000::StringList& listKey, ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    if (listKey.empty())
    {
        ZGLOG_ERROR("Invalid entry param");
        return false;
    }
    listValue.reserve(listKey.size());
    for (size_t i = 0; i < listKey.size(); ++i)
    {
        std::string value;
        if (!getDataByKeyToValue(listKey[i], value, e))
        {
            ZGLOG_ERROR(e);
            return false;
        }
        listValue.emplace_back(value);
    }
    return true;
}

bool ZGSPRTDataMng::mgetDataByKeyToMap(const ::ZG6000::StringList& listKey, ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::mgetDataByKeyToJson(const ::ZG6000::StringList& listKey, ::std::string& jsonValue, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::msetDataByKeyFromList(const ::ZG6000::StringList& listKey, const ::ZG6000::StringList& listValue, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    if (listKey.empty() || listKey.size() != listValue.size())
    {
        ZGLOG_ERROR("Invalid entry param");
        return false;
    }
    for (size_t i = 0; i < listKey.size(); ++i)
    {
        setDataByKeyFromValue(listKey[i], listValue[i], e);
    }
    return true;
}

bool ZGSPRTDataMng::msetDataByKeyFromMap(const ::ZG6000::StringMap& keyValue, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    if (keyValue.empty())
    {
        ZGLOG_ERROR("Invalid entry param");
        return false;
    }
    for (const auto& pair : keyValue)
    {
        setDataByKeyFromValue(pair.first, pair.second, e);
    }
    return true;
}

bool ZGSPRTDataMng::getDataByIDReply(const ::std::string& tableName, const ::std::string& id,
    ZG6000::ErrorInfo& e, const ReplyFunc& func)
{
    return execRedisCommand([&](redisContext* context)->bool
    {
        std::string key = tableName + "/" + id;
        if (!doAppendRedisCommand(e, context, "HGETALL %b", key.c_str(), key.size()))
            return false;
        redisReply* reply = doGetRedisReply(e, context);
        if (reply == nullptr)
            return false;
        bool result = true;
        if (func)
            result = func(reply);
        freeReplyObject(reply);
        return result;
    }, e);
}

bool ZGSPRTDataMng::getDataByIDToMap(const ::std::string& tableName, const ::std::string& id,
                                ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e)
{
    return getDataByIDReply(tableName, id, e, [&](redisReply* reply)->bool
    {
        if (reply->type != REDIS_REPLY_ARRAY)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = "Reply type is not array.";
            return false;
        }
        if (reply->elements > 0)
        {
            for (std::size_t i = 0; i < reply->elements; i += 2)
            {
                const auto & element1 = reply->element[i];
                if ((int64_t)element1 == 0 || (int64_t)element1 == -1)
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                    e.errDetail = "invalid key or value.";
                    return false;
                }
                const auto & element2 = reply->element[i + 1];
                if ((int64_t)element2 == 0 || (int64_t)element2 == -1)
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                    e.errDetail = "invalid key or value.";
                    return false;
                }
                if (element1->type != REDIS_REPLY_STRING)
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                    e.errDetail = "Reply type is not string.";
                    return false;
                }
                if (element2->type != REDIS_REPLY_STRING)
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                    e.errDetail = "Reply type is not string.";
                    return false;
                }
                if ((reinterpret_cast<intptr_t>(reply->element[i]->str) == 0) || (reinterpret_cast<intptr_t>(reply->element[i]->str) == -1)
                    || (reinterpret_cast<intptr_t>(reply->element[i + 1]->str) == 0) || (reinterpret_cast<intptr_t>(reply->element[i + 1]->str) == -1))
                {
                    e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                    e.errDetail = "invalid key or value.";
                    return false;
                }
                mapValue.insert(std::make_pair(reply->element[i]->str, reply->element[i + 1]->str));
            }
        }
        return true;
    });
}

bool ZGSPRTDataMng::getDataByIDToJson(const ::std::string& tableName, const ::std::string& id,
                                      ::std::string& jsonValue, ZG6000::ErrorInfo& e)
{
    return getDataByIDReply(tableName, id, e, [&](redisReply* reply)->bool
    {
        if (reply->type != REDIS_REPLY_ARRAY)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = "Reply type is not array.";
            return false;
        }
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writer.StartArray();
        if (reply->elements > 0)
        {
            writer.StartObject();
            for (std::size_t i = 0; i < reply->elements; i += 2)
            {
                writer.Key(reply->element[i]->str);
                writer.String(reply->element[i + 1]->str);
            }
            writer.EndObject();
        }
        writer.EndArray();
        jsonValue = buffer.GetString();
        return true;
    });
}

bool ZGSPRTDataMng::getDataByFieldReply(const ::std::string& tableName, const ::std::string& id,
                                        const ::std::string& fieldName, ::std::string& fieldValue, ZG6000::ErrorInfo& e,
                                        const ReplyFunc& func)
{
    return execRedisCommand([&](redisContext* context)->bool
    {
        std::string key = tableName + "/" + id;
        if (!doAppendRedisCommand(e, context, "HGET %b %b", key.c_str(), key.size(), fieldName.c_str(), fieldName.size()))
            return false;
        redisReply* reply = doGetRedisReply(e, context);
        if (reply == nullptr)
            return false;
        if ((reply->type == REDIS_REPLY_NIL) || (reply->str == nullptr))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = "Key '" + key + "' or field '" + fieldName + "' is not exist.";
            return false;
        }
        fieldValue = reply->str;
        bool result = true;
        if (func)
            result = func(reply);
        freeReplyObject(reply);
        return result;
    }, e);
}

bool ZGSPRTDataMng::getDataByFieldToValue(const ::std::string& tableName, const ::std::string& id,
                                   const ::std::string& fieldName, ::std::string& fieldValue, ZG6000::ErrorInfo& e)
{
    return getDataByFieldReply(tableName, id, fieldName, fieldValue, e, nullptr);
}

bool ZGSPRTDataMng::getDataByFieldToJson(const ::std::string& tableName, const ::std::string& id,
                                         const ::std::string& fieldName, ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e)
{
    std::string fieldValue = "";
    return getDataByFieldReply(tableName, id, fieldName, fieldValue, e, [&](redisReply* reply)->bool
    {
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writer.StartArray();
        writer.StartObject();
        writer.Key(fieldName.c_str());
        writer.String(fieldValue.c_str());
        writer.EndObject();
        writer.EndArray();
        jsonFieldValue = buffer.GetString();
        return true;
    });
}

bool ZGSPRTDataMng::getDataByFieldsReply(const ::std::string& tableName, const ::std::string& id,
    const ::ZG6000::StringList& listFieldName, ZG6000::ErrorInfo& e, const ReplyFunc& func)
{
    return execRedisCommand([&](redisContext* context)->bool
    {
        std::string key = tableName + "/" + id;    
        std::string command = "HMGET " + key;    
        for (const auto& fieldName : listFieldName)
        {
            command += " " + fieldName;
        }
        if (!doAppendRedisCommand(e, context, command.c_str()))
            return false;
        redisReply* reply = doGetRedisReply(e, context);
        if (reply == nullptr)
            return false;
        bool result = true;
        if (func)
            result = func(reply);
        freeReplyObject(reply);
        if (!result)
            redisReconnect(context);
        return result;
    }, e);
}

bool ZGSPRTDataMng::getDataByFieldsToList(const ::std::string& tableName, const ::std::string& id,
                                    const ::ZG6000::StringList& listFieldName, ::ZG6000::StringList& listFieldValue, ZG6000::ErrorInfo& e)
{
    return getDataByFieldsReply(tableName, id, listFieldName, e, [&](redisReply* reply)->bool
    {
        if (reply->elements > 0)
        {
            listFieldValue.reserve(reply->elements);
            for (std::size_t i = 0; i < reply->elements; ++i)
            {
                if (reply->element[i]->type == REDIS_REPLY_NIL)
                    listFieldValue.emplace_back("");
                else
                    listFieldValue.emplace_back(reply->element[i]->str);
            }
        }
        return true;
    });
}

bool ZGSPRTDataMng::getDataByFieldsToMap(::std::string tableName, ::std::string id, ZG6000::StringList listFieldName,
    ZG6000::StringMap& mapFieldValue, ZG6000::ErrorInfo& e)
{
    return getDataByFieldsReply(tableName, id, listFieldName, e, [&](redisReply* reply)->bool
    {
        if (reply->elements != listFieldName.size())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = QString("Field size is not equal to value size, expect %1, actual %2").arg(listFieldName.size()).arg(reply->elements).toStdString();
            ZGLOG_ERROR(e);
            return false;
        }
        if (reply->elements > 0)
        {
            for (std::size_t i = 0; i < reply->elements; ++i)
            {
                if (reply->element[i]->type == REDIS_REPLY_NIL)
                    mapFieldValue[listFieldName[i]] = "";
                else
                    mapFieldValue[listFieldName[i]] = reply->element[i]->str;
            }
        }
        return true;
    });
}

bool ZGSPRTDataMng::getDataByFieldsToJson(const ::std::string& tableName, const ::std::string& id,
                                          const ::ZG6000::StringList& listFieldName, ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e)
{
    return getDataByFieldsReply(tableName, id, listFieldName, e, [&](redisReply* reply)->bool
    {
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writer.StartArray();
        if (reply->elements > 0)
        {
            writer.StartObject();
            for (std::size_t i = 0; i < reply->elements; ++i)
            {
                writer.Key(listFieldName[i].c_str());
                writer.String(reply->element[i]->str);
            }
            writer.EndObject();
        }
        writer.EndArray();
        jsonFieldValue = buffer.GetString();
        return true;
    });
}

bool ZGSPRTDataMng::mgetDataByIDsReply(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                       ZG6000::ErrorInfo& e, const std::function<bool(const ::ZG6000::StringList& fieldNames)>& func)
{
    if (listID.size() > m_maxRecordNum)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = QString("ID size exceeds the max limit range(%1)").arg(m_maxRecordNum).toStdString();
        return false;
    }
    ZG6000::StringList fieldNames;
    if (!getCachedTableFields(tableName, fieldNames, e))
        return false;
    if (func)
        return func(fieldNames);
    return true;
}

bool ZGSPRTDataMng::mgetDataByIDToListList(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                  ::ZG6000::ListStringList& listFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByIDsReply(tableName, listID, e, [&](const ZG6000::StringList& fieldNames)->bool
    {
        return mgetDataByFieldsToListList(tableName, listID, fieldNames, listFieldValue, e);
    });
}

bool ZGSPRTDataMng::mgetDataByIDToJson(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                        ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByIDsReply(tableName, listID, e, [&](const ZG6000::StringList& fieldNames)->bool
    {
        return mgetDataByFieldsToJson(tableName, listID, fieldNames, jsonFieldValue, e);
    });
}

bool ZGSPRTDataMng::mgetDataByIDToListMap(::std::string tableName, ZG6000::StringList listID,
                                     ZG6000::ListStringMap& listFieldMapValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByIDsReply(tableName, listID, e, [&](const ZG6000::StringList& fieldNames)->bool
    {
        return mgetDataByFieldsToListMap(tableName, listID, fieldNames, listFieldMapValue, e);
    });
}

bool ZGSPRTDataMng::mgetDataByIDToMapMap(::std::string tableName, ZG6000::StringList listID,
    ZG6000::MapStringMap& mapFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByIDsReply(tableName, listID, e, [&](const ZG6000::StringList& fieldNames)->bool
    {
        return mgetDataByFieldsToMapMap(tableName, listID, fieldNames, mapFieldValue, e);
    });
}

bool ZGSPRTDataMng::mgetDataByFieldReply(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                         const ::std::string& fieldName, ZG6000::ErrorInfo& e, const ZGSPRTDataMng::ContextFunc& func)
{
    if (listID.size() > m_maxRecordNum)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = QString("ID size exceeds the max limit range(%1)").arg(m_maxRecordNum).toStdString();
        return false;
    }
    return execRedisCommand([&](redisContext* context)->bool
    {
        for (const auto& id : listID)
        {
            std::string key = tableName + "/" + id;
            if (!doAppendRedisCommand(e, context, "HGET %b %b", key.data(), key.size(), 
                                     fieldName.data(), fieldName.size()))
                return false;
        }
        if (func)
            return func(context);
        return true;
    }, e);
}

bool ZGSPRTDataMng::mgetDataByFieldToList(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                    const ::std::string& fieldName, ::ZG6000::StringList& listFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByFieldReply(tableName, listID, fieldName, e, [&](redisContext* context)->bool
    {
        size_t count = listID.size();
        return multiReplyToStringList(context, count, listFieldValue, e);
    });
}

bool ZGSPRTDataMng::mgetDataByFieldToMap(::std::string tableName, ZG6000::StringList listID, ::std::string fieldName,
    ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByFieldReply(tableName, listID, fieldName, e, [&](redisContext* context)->bool
    {
        size_t count = listID.size();
        ZG6000::StringList listFieldValue;
        if (!multiReplyToStringList(context, count, listFieldValue, e))
            return false;
        if (listID.size() != listFieldValue.size())
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = "The size of listID and listFieldValue is not equal.";
            ZGLOG_ERROR(e);
            return false;
        }
        mapValue.clear();
        for (size_t i = 0; i < listID.size(); ++i)
        {
            mapValue[listID[i]] = listFieldValue[i];
        }
        return true;
    });
}

bool ZGSPRTDataMng::mgetDataByFieldToJson(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                          const ::std::string& fieldName, ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByFieldReply(tableName, listID, fieldName, e, [&](redisContext* context)->bool
    {
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writer.StartArray();
        size_t count = listID.size();
        ZG6000::StringList fieldValues;
        if (!multiReplyToStringList(context, count, fieldValues, e))
            return false;
        for (const auto& fieldValue : fieldValues)
        {
            writer.StartObject();
            writer.Key(fieldName.c_str());
            writer.String(fieldValue.c_str());
            writer.EndObject();
        }
        writer.EndArray();
        jsonFieldValue = buffer.GetString();
        return true;
    });
}

bool ZGSPRTDataMng::mgetDataByFieldsReply(const ::std::string& tableName, const ::ZG6000::StringList& listID,
    const ::ZG6000::StringList& listFieldName, ZG6000::ErrorInfo& e, const ContextFunc& func)
{
    if (listID.size() > m_maxRecordNum)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = QString("ID size exceeds the max limit range(%1)").arg(m_maxRecordNum).toStdString();
        return false;
    }
    return execRedisCommand([&](redisContext* context)->bool
    {
        std::string fields{""};
        for (const auto& fieldName : listFieldName)
        {
            fields += " " + fieldName;
        }
        for (const auto& id : listID)
        {
            std::string key = tableName + "/" + id;
            std::string command = "hmget " + key + fields;
            if (!doAppendRedisCommand(e, context, command.c_str()))
                return false;
        }
        bool result = false;
        if (func)
            result = func(context);
        if (!result)
            redisReconnect(context);
        return true;
    }, e);
}

void ZGSPRTDataMng::onCheckTable()
{
    syncData();
}

bool ZGSPRTDataMng::mgetDataByFieldsToListList(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                               const ::ZG6000::StringList& listFieldName, ::ZG6000::ListStringList& listFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByFieldsReply(tableName, listID, listFieldName, e, [&](redisContext* context)->bool
    {
        listFieldValue.reserve(listID.size());
        for (size_t i = 0; i < listID.size(); ++i)
        {
            redisReply* reply = doGetRedisReply(e, context);
            if (reply == nullptr)
                return false;
            ZG6000::StringList fieldsValue;
            if (!replyArrayToStringList(reply, fieldsValue, e))
                return false;
            listFieldValue.emplace_back(fieldsValue);
            freeReplyObject(reply);
        }
        return true;
    });
}

bool ZGSPRTDataMng::mgetDataByFieldsToJson(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                           const ::ZG6000::StringList& listFieldName, ::std::string& jsonFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByFieldsReply(tableName, listID, listFieldName, e, [&](redisContext* context)->bool
    {
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writer.StartArray();
        for (size_t i = 0; i < listID.size(); ++i)
        {
            redisReply* reply = doGetRedisReply(e, context);
            if (reply == nullptr)
                return false;
            ZG6000::StringList recordValues;
            if (!replyArrayToStringList(reply, recordValues, e))
                return false;
            if (listFieldName.size() != recordValues.size())
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                e.errDetail = QString("Field size is not equal to value size, expect %1, actual %2").arg(listFieldName.size()).arg(recordValues.size()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            writer.StartObject();
            for (size_t j = 0; j < listFieldName.size(); ++j)
            {
                writer.Key(listFieldName[j].c_str());
                writer.String(recordValues[j].c_str());
            }
            writer.EndObject();
            freeReplyObject(reply);
        }
        writer.EndArray();
        jsonFieldValue = buffer.GetString();
        return true;
    });
}

bool ZGSPRTDataMng::mgetDataByFieldsToListMap(::std::string tableName, ZG6000::StringList listID,
                                        ZG6000::StringList listFieldName, ZG6000::ListStringMap& listFieldMapValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByFieldsReply(tableName, listID, listFieldName, e, [&](redisContext* context)->bool
    {
        listFieldMapValue.clear();
        listFieldMapValue.reserve(listID.size());
        for (size_t i = 0; i < listID.size(); ++i)
        {
            redisReply* reply = doGetRedisReply(e, context);
            if (reply == nullptr)
                return false;
            ZG6000::StringList fieldsValue;
            if (!replyArrayToStringList(reply, fieldsValue, e))
                return false;
            if (listFieldName.size() != fieldsValue.size())
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                e.errDetail = QString("Field size is not equal to value size, expect %1, actual %2").arg(listFieldName.size()).arg(fieldsValue.size()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZG6000::StringMap fieldMapValue;
            for (size_t j = 0; j < listFieldName.size(); ++j)
            {
                fieldMapValue[listFieldName[j]] = fieldsValue[j];
            }
            listFieldMapValue.emplace_back(fieldMapValue);
            freeReplyObject(reply);
        }
        return true;
    });
}

bool ZGSPRTDataMng::mgetDataByFieldsToMapMap(::std::string tableName, ZG6000::StringList listID,
    ZG6000::StringList listFieldName, ZG6000::MapStringMap& mapFieldValue, ZG6000::ErrorInfo& e)
{
    return mgetDataByFieldsReply(tableName, listID, listFieldName, e, [&](redisContext* context)->bool
    {
        mapFieldValue.clear();
        for (size_t i = 0; i < listID.size(); ++i)
        {
            redisReply* reply = doGetRedisReply(e, context);
            if (reply == nullptr)
                return false;
            ZG6000::StringList fieldsValue;
            if (!replyArrayToStringList(reply, fieldsValue, e))
                return false;
            if (listFieldName.size() != fieldsValue.size())
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                e.errDetail = QString("Field size is not equal to value size, expect %1, actual %2").arg(listFieldName.size()).arg(fieldsValue.size()).toStdString();
                ZGLOG_ERROR(e);
                return false;
            }
            ZG6000::StringMap fieldMapValue;
            for (size_t j = 0; j < listFieldName.size(); ++j)
            {
                fieldMapValue[listFieldName[j]] = fieldsValue[j];
            }
            mapFieldValue[listID[i]] = fieldMapValue;
            freeReplyObject(reply);
        }
        return true;
    });
}

void writeStart(Writer<StringBuffer>& writer, const std::string& table, const std::string& oper)
{
    writer.StartObject();
    writer.Key("operation");
    writer.String(oper.c_str());
    writer.Key("table");
    writer.String(table.c_str());
    writer.Key("reason");
    writer.String("change");
    writer.Key("time");
    QString now = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    std::string strNow = now.toStdString();
    writer.String(strNow.c_str());
    writer.Key("items");
    writer.StartArray();
}

void writeRowStart(Writer<StringBuffer>& writer, const std::string& id, const std::string& oper)
{
    writer.StartObject();
    writer.Key("id");
    writer.StartArray();
    writer.String(id.c_str());
    writer.String("");
    writer.EndArray();
}

void writeUpdateElem(Writer<StringBuffer>& writer, const char* fieldName, const char* oldValue, const char* newValue)
{
    writer.Key(fieldName);
    writer.StartArray();
    writer.String(newValue);
    if (!oldValue)
        writer.String("");
    else
        writer.String(oldValue);
    writer.EndArray();
}

void writeInsertElem(Writer<StringBuffer>& writer, const char* fieldName, const char* value)
{
    writer.Key(fieldName);
    writer.StartArray();
    writer.String(value);
    writer.String("");
    writer.EndArray();
}

void writeEndElem(Writer<StringBuffer>& writer)
{
    writer.EndArray();
    writer.EndObject();
}

void writeValueChangedToJson(Writer<StringBuffer>& writer, redisReply* reply, const std::string& tableName, const std::string& id, const char* fieldName, const char* newValue, bool& totalChanged, bool& rowChanged, const std::string& oper)
{
    if (reply->type == REDIS_REPLY_NIL || reply->str == nullptr
        || strcmp(reply->str, newValue) != 0)
    {
        if (!totalChanged)
        {
            totalChanged = true;
            writeStart(writer, tableName, oper);
        }
        if (!rowChanged)
        {
            rowChanged = true;
            writeRowStart(writer, id, oper);
        }
        if (oper == "update")
            writeUpdateElem(writer, fieldName, reply->str, newValue);
        else if (oper == "insert")
            writeInsertElem(writer, fieldName, newValue);
    }
}

bool ZGSPRTDataMng::updateDataByFieldFromValue(const ::std::string& tableName, const ::std::string& id,
                                      const ::std::string& fieldName, const ::std::string& fieldValue, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    ZG6000::StringMap mapValue;
    mapValue[fieldName] = fieldValue;
    return updateDataByIDFromMap(tableName, id, mapValue, e);
}

bool ZGSPRTDataMng::mupdateDataByFieldFromValue(::std::string tableName, ZG6000::StringList listID, ::std::string fieldName,
    ::std::string fieldValue, ZG6000::ErrorInfo& e)
{
    return execRedisTransaction([&](redisContext* context)->bool
    {
        for (size_t i = 0; i < listID.size(); ++i)
        {
            if (!appendMultiCommand(context, tableName, listID[i], fieldName, fieldValue))
                return false;
        }
        if (!verifyQueuedCommand(context, 2 * listID.size(), e))
            return false;
        return true;
    }, [&](redisContext* context, redisReply* reply)->bool
    {
        bool totalChanged = false;
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        if (reply->elements != (listID.size() * 2))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = "Reply elements mismatched.";
            return false;
        }
        for (size_t i = 0; i < reply->elements / 2; ++i)
        {
            bool rowChanged = false;
            writeValueChangedToJson(writer, reply->element[i * 2], tableName, listID[i], fieldName.c_str(), fieldValue.c_str(), totalChanged, rowChanged, "update");
            if (rowChanged)
                writer.EndObject();
        }
        if (totalChanged)
        {
            writeEndElem(writer);
            long long subscriberNum;
            std::string errMsg;
            m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//            redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//            freeReplyObject(pubReply);
        }
        return true;
    }, e);
}

bool ZGSPRTDataMng::execRedisTransaction(const ContextFunc& contextFunc, const ZGSPRTDataMng::ContextReplyFunc& replyFunc,
                                         ZG6000::ErrorInfo& e)
{
    return execRedisCommand([&](redisContext* context)->bool
    {
        redisReply* reply = doSendRedisCommand(e, context, "MULTI");
        if (reply == nullptr)
            return false;
        freeReplyObject(reply);
        if (contextFunc)
        {
            if (!contextFunc(context))
            {
                reply = doSendRedisCommand(e, context, "DISCARD");
                freeReplyObject(reply);
                return false;
            }
        }
        reply = doSendRedisCommand(e, context, "EXEC");
        if (reply == nullptr)
            return false;
        bool success = true;
        if (replyFunc)
        {
            success = replyFunc(context, reply);
        }
        freeReplyObject(reply);
        return success;
    }, e, true);
}

// Redis* ZGSPRTDataMng::getThreadRedisClient()
// {
//     Qt::HANDLE threadID = QThread::currentThreadId();
//     Redis* client = findThreadRedisClient(threadID);
//     if (client)
//         return client;
//     ZGLOG_INFO("Can't find redis object, create one...");
//     client = new Redis(m_connectionOptions, 2);
//     QThread::msleep(100);
//     setThreadRedisClient(threadID, client);
//     return client;
// }
//
// Redis* ZGSPRTDataMng::findThreadRedisClient(Qt::HANDLE threadID)
// {
//     QReadLocker locker(&m_lock);
//     auto pair = m_mapThreadClient.find(threadID);
//     if (pair == m_mapThreadClient.end())
//         return nullptr;
//     return pair->second;
// }
//
// void ZGSPRTDataMng::setThreadRedisClient(Qt::HANDLE threadID, Redis* client)
// {
//     QWriteLocker locker(&m_lock);
//     m_mapThreadClient[threadID] = client;
// }

bool ZGSPRTDataMng::updateDataByIDFromMap(const ::std::string& tableName, const ::std::string& id,
                                          const ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e)
{
    if (mapValue.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Invalid entry param, mapValue is Empty.";
        return false;
    }
    return execRedisCommand([&](redisContext* context)->bool
    {       
        std::string key = tableName + "/" + id;
        std::vector<const char* > vGetCommand, vSetCommand;
        vGetCommand.resize(mapValue.size() + 2, "");
        vGetCommand[0] = "HMGET";
        vGetCommand[1] = key.c_str();
        vSetCommand.resize(mapValue.size() * 2 + 2, "");
        vSetCommand[0] = "HMSET";
        vSetCommand[1] = key.c_str();
        size_t index = 1;
        for (const auto& fieldPair : mapValue)
        {
            vGetCommand[index + 1] = fieldPair.first.c_str();
            vSetCommand[2 * index] = fieldPair.first.c_str();
            vSetCommand[2 * index + 1] = fieldPair.second.c_str();
            ++index;
        }
        if (!doAppendRedisCommand(e, context, static_cast<int>(vGetCommand.size()), vGetCommand.data(), nullptr))
            return false;
        if (!doAppendRedisCommand(e, context, static_cast<int>(vSetCommand.size()), vSetCommand.data(), nullptr))
            return false;         
        redisReply* replyGet = doGetRedisReply(e, context);
        if (replyGet == nullptr)
            return false;
        redisReply* replySet = doGetRedisReply(e, context);
        if (replySet == nullptr)
            return false;
        if ((replySet->type != REDIS_REPLY_STATUS))
        {
            if ((replySet->str == nullptr) || (strcmp(replySet->str, "OK") != 0))
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                e.errDetail = "Reply type mismatched.";
                freeReplyObject(replyGet);
                freeReplyObject(replySet);
                return false;
            }
        }
        freeReplyObject(replySet);
        bool dataChanged = false, rowChanged = false;
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        auto pair = mapValue.begin();
        index = 0;
        while (pair != mapValue.end())
        {
            writeValueChangedToJson(writer, replyGet->element[index], tableName, id, pair->first.c_str(), pair->second.c_str(), dataChanged, rowChanged, "update");
            ++index;
            ++pair;
        }
        freeReplyObject(replyGet);
        if (dataChanged)
        {
            writer.EndObject();
            writeEndElem(writer);
            long long subscriberNum;
            std::string errMsg;
            m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
        }
        return true;
    }, e, true);
}

// bool ZGSPRTDataMng::updateDataByIDFromMap(const ::std::string& tableName, const ::std::string& id,
//                                    const ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e)
// {
//     if (mapValue.empty())
//     {
//         e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
//         e.errDetail = "Invalid entry param.";
//         return false;
//     }
//     return execRedisTransaction([&](redisContext* context)->bool
//     {
//         if (!appendMultiCommand(context, tableName, id, mapValue))
//             return false;
//         if (!verifyQueuedCommand(context, 2, e))
//             return false;
//         return true;
//     }, [&](redisContext* context, redisReply* reply)->bool
//     {
//         if (reply->element[1]->type != REDIS_REPLY_STATUS && strcmp(reply->element[1]->str, "OK") != 0)
//         {
//             e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
//             e.errDetail = "Reply type mismatched.";
//             return false;
//         }
//         std::vector<const char*> fieldNames, fieldValues;
//         splitMap(mapValue, fieldNames, fieldValues);
//         bool dataChanged = false, rowChanged = false;
//         StringBuffer buffer;
//         Writer<StringBuffer> writer(buffer);
//         for (size_t i = 0; i < reply->element[0]->elements; i++)
//         {
//             writeValueChangedToJson(writer, reply->element[0]->element[i], tableName, id, 
//                                     fieldNames[i], fieldValues[i], dataChanged, rowChanged, "update");
//         }
//         if (dataChanged)
//         {
//             writer.EndObject();
//             writeEndElem(writer);
//             long long subscriberNum;
//             std::string errMsg;
//             m_pRedisClient->publish(tableName, buffer.GetString(), subscriberNum, errMsg);
// //            redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
// //            freeReplyObject(pubReply);
//         }
//         return true;
//     }, e);
// }

bool ZGSPRTDataMng::mupdateDataByFieldFromList(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                       const ::std::string& fieldName, const ::ZG6000::StringList& listFieldValue, ZG6000::ErrorInfo& e)
{
    if (listID.empty() || listID.size() > m_maxRecordNum || listID.size() != listFieldValue.size())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Invalid entry param.";
        return false;
    }
    return execRedisTransaction([&](redisContext* context)->bool
    {
        for (size_t i = 0; i < listID.size(); ++i)
        {
            if (!appendMultiCommand(context, tableName, listID[i], fieldName, listFieldValue[i]))
                return false;
        }
        if (!verifyQueuedCommand(context, 2 * listID.size(), e))
            return false;
        return true;
    }, [&](redisContext* context, redisReply* reply)->bool
    {
        bool totalChanged = false;
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        for (size_t i = 0; i < reply->elements / 2; ++i)
        {
            bool rowChanged = false;
            writeValueChangedToJson(writer, reply->element[i * 2], tableName, listID[i], fieldName.c_str(), listFieldValue[i].c_str(), totalChanged, rowChanged, "update");
            if (rowChanged)
                writer.EndObject();
        }
        if (totalChanged)
        {
            writeEndElem(writer);
            long long subscriberNum;
            std::string errMsg;
            m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//            redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//            freeReplyObject(pubReply);
        }
        return true;
    }, e);
}

bool ZGSPRTDataMng::mupdateValueByFieldsFromListList(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                            const ::ZG6000::StringList& listFieldName, const ::ZG6000::ListStringList& listFieldValue, ZG6000::ErrorInfo& e)
{
    if (listID.empty() || listID.size() > 1000 || listFieldName.empty() || listFieldValue.empty() || listID.size() != listFieldValue.size())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Invalid entry param.";
        return false;
    }
    return execRedisTransaction([&](redisContext* context)->bool
    {
        std::vector<const char* > vGetCommand, vSetCommand;
        vGetCommand.resize(listFieldName.size() + 2);
        vSetCommand.resize(listFieldName.size() * 2 + 2);
        vGetCommand[0] = "hmget";
        vSetCommand[0] = "hmset";
        for (size_t i = 0; i < listFieldName.size(); ++i)
        {
            vGetCommand[2 + i] = listFieldName[i].c_str();
            vSetCommand[2 + i * 2] = listFieldName[i].c_str();
        }
        for (size_t i = 0; i < listID.size(); ++i)
        {
            if (!appendMultiCommand(context, tableName, listID[i], vGetCommand, vSetCommand, listFieldValue[i]))
                return false;
        }
        if (!verifyQueuedCommand(context, 2 * listID.size(), e))
            return false;
        return true;
    }, [&](redisContext* context, redisReply* reply)->bool
    {
        bool totalChanged = false;
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        for (size_t i = 0; i < reply->elements / 2; ++i)
        {
            bool rowChanged = false;
            for (size_t j = 0; j < reply->element[i * 2]->elements; ++j)
            {
                writeValueChangedToJson(writer, reply->element[i * 2]->element[j], tableName, 
                    listID[i], listFieldName[j].c_str(), listFieldValue[i][j].c_str(), totalChanged, rowChanged, "update");
            }
            if (rowChanged)
                writer.EndObject();
        }
        if (totalChanged)
        {
            writeEndElem(writer);
            long long subscriberNum;
            std::string errMsg;
            m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//            redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//            freeReplyObject(pubReply);
        }
        return true;
    }, e);
}

bool ZGSPRTDataMng::mupdateDataByFieldsFromListMap(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                        const ::ZG6000::ListStringMap& listFieldMapValue, ZG6000::ErrorInfo& e)
{
    e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
    if (listID.empty())
    {
        e.errDetail = "listID is empty";
        return false;
    }
    if (listID.size() > m_maxRecordNum)
    {
        e.errDetail = QString("ID size exceeds the max limit range(%1)").arg(m_maxRecordNum).toStdString();
        return false;
    }
    if (listID.size() != listFieldMapValue.size())
    {
        e.errDetail = "listID size not equal listFieldMapValue.size()," + std::to_string(listID.size()) + "," + std::to_string(listFieldMapValue.size());
        return false;
    }
    return execRedisTransaction([&](redisContext* context)->bool
    {
        for (size_t i = 0; i < listID.size(); ++i)
        {
            if (!appendMultiCommand(context, tableName, listID[i], listFieldMapValue[i]))
                return false;
        }
        if (!verifyQueuedCommand(context, 2 * listID.size(), e))
            return false;
        return true;
    }, [&](redisContext* context, redisReply* reply)->bool
    {
        std::vector<std::vector<const char*>> listFieldNames, listFieldValues;
        getMapListKeys(listFieldMapValue, listFieldNames, listFieldValues);
        bool totalChanged = false;
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        if (reply->elements != listID.size() * 2)
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = "Reply size mismatched.";
            ZGLOG_ERROR(e);
            return false;
        }
        for (size_t i = 0; i < reply->elements / 2; ++i)
        {
            bool rowChanged = false;
            if (reply->element[i * 2]->elements != listFieldNames[i].size())
            {
                e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
                e.errDetail = "Reply size mismatched.";
                ZGLOG_ERROR(e);
                return false;
            }
            for (size_t j = 0; j < reply->element[i * 2]->elements; ++j)
            {
                writeValueChangedToJson(writer, reply->element[i * 2]->element[j], tableName, listID[i], 
                    listFieldNames[i][j], listFieldValues[i][j], totalChanged, rowChanged, "update");
            }
            if (rowChanged)
                writer.EndObject();
        }
        if (totalChanged)
        {
            writeEndElem(writer);
            long long subscriberNum;
            std::string errMsg;
            m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//            redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//            freeReplyObject(pubReply);
        }
        return true;
    }, e);
}

bool ZGSPRTDataMng::mupdateDataByFieldsFromMap(::std::string tableName, ZG6000::StringList listID,
    ZG6000::StringMap fieldMapValue, ZG6000::ErrorInfo& e)
{
    if (listID.empty() || listID.size() > m_maxRecordNum || fieldMapValue.empty())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Invalid entry param.";
        return false;
    }
    return execRedisTransaction([&](redisContext* context)->bool
    {
        for (size_t i = 0; i < listID.size(); ++i)
        {
            if (!appendMultiCommand(context, tableName, listID[i], fieldMapValue))
                return false;
        }
        if (!verifyQueuedCommand(context, 2 * listID.size(), e))
            return false;
        return true;
    }, [&](redisContext* context, redisReply* reply)->bool
    {
        std::vector<const char*> fieldNames;
        std::vector<const char*> fieldValues;
        splitMap(fieldMapValue, fieldNames, fieldValues);
        bool totalChanged = false;
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        for (size_t i = 0; i < reply->elements / 2; ++i)
        {
            bool rowChanged = false;
            for (size_t j = 0; j < reply->element[i * 2]->elements; ++j)
            {
                writeValueChangedToJson(writer, reply->element[i * 2]->element[j], tableName, listID[i], 
                    fieldNames[j], fieldValues[j], totalChanged, rowChanged, "update");
            }
            if (rowChanged)
                writer.EndObject();
        }
        if (totalChanged)
        {
            writeEndElem(writer);
            long long subscriberNum;
            std::string errMsg;
            m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//            redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//            freeReplyObject(pubReply);
        }
        return true;
    }, e);
}

bool ZGSPRTDataMng::updateDataID(::std::string tableName, ::std::string oldID, ::std::string newID,
                                 ZG6000::ErrorInfo& e)
{
    ZG6000::StringList listOldID, listNewID;
    listOldID.push_back(oldID);
    listNewID.push_back(newID);
    return mupdateDataID(tableName, listOldID, listNewID, e);
}

bool ZGSPRTDataMng::mupdateDataID(::std::string tableName, ZG6000::StringList listOldID, ZG6000::StringList listNewID,
    ZG6000::ErrorInfo& e)
{
    if (listOldID.size() > m_maxRecordNum || (listOldID.size() != listNewID.size()))
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Invalid entry param.";
        return false;
    }
    ZG6000::StringList listRealOldID, listRealNewID;
    for (size_t i = 0; i < listOldID.size(); ++i)
    {
        if (listOldID[i] != listNewID[i])
        {
            listRealOldID.emplace_back(listOldID[i]);
            listRealNewID.emplace_back(listNewID[i]);
        }
    }
    return execRedisTransaction([&](redisContext* context)->bool
    {        
        std::string field = "id";
        for (size_t i = 0; i < listRealOldID.size(); ++i)
        {
            std::string oldKey = tableName + "/" + listRealOldID[i];
            std::string newKey = tableName + "/" + listRealNewID[i];
            if (!doAppendRedisCommand(e, context, "RENAME %b %b", oldKey.data(), oldKey.size(), newKey.data(), newKey.size()))
                return false;
            if (!doAppendRedisCommand(e, context, "HSET %b %b %b", newKey.data(), newKey.size(), field.data(), field.size(), listRealNewID[i].data(), listRealNewID[i].size()))
                return false;
        }
        if (!verifyQueuedCommand(context, listRealOldID.size() * 2, e))
            return false;
        return true;
    }, [&](redisContext* context, redisReply* reply)->bool
    {
        bool totalChanged = false;
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        for (size_t i = 0; i < reply->elements / 2; ++i)
        {
            if (reply->element[i * 2]->type != REDIS_REPLY_ERROR && reply->element[i * 2 + 1]->type != REDIS_REPLY_ERROR)
            {
                if (!totalChanged)
                {
                    totalChanged = true;
                    writeStart(writer, tableName, "update");
                }
                writer.StartObject();
                writeUpdateElem(writer, "id", listRealOldID[i].c_str(), listRealNewID[i].c_str());
                writer.EndObject();
            }
        }
        if (totalChanged)
        {
            writeEndElem(writer);
            long long subscriberNum;
            std::string errMsg;
            m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//            redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//            freeReplyObject(pubReply);
        }
        return true;
    }, e);
}

bool ZGSPRTDataMng::insertDataByFieldFromValue(const ::std::string& tableName, const ::std::string& id, const ::std::string& fieldName,
                                      const ::std::string& fieldValue, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    ZG6000::StringMap mapValue;
    mapValue[fieldName] = fieldValue;
    return insertDataByIDFromMap(tableName, id, mapValue, e);
}

bool ZGSPRTDataMng::insertDataByIDFromMap(const ::std::string& tableName, const ::std::string& id,
                                   const ::ZG6000::StringMap& mapValue, ZG6000::ErrorInfo& e)
{
    // std::string key = tableName + "/" + id;
    // auto conn = m_pClient->connection();
    // if (!conn)
    // {
    //     e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_CONN);
    //     e.errDetail = "Can't get redis connection.";
    //     return false;
    // }
    // try
    // {
    //     if (conn->exists(key))
    //     {
    //         e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
    //         e.errDetail = "key " + key + " is already exists.";
    //         return false;
    //     }
    // }
    // catch (std::exception& ex)
    // {
    //     e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_EXEC);
    //     e.errDetail = ex.what();
    //     return false;
    // }
    ZG6000::ListStringMap listFieldMapValue;
    listFieldMapValue.push_back(mapValue);
    ZG6000::StringList listID;
    listID.push_back(id);
    return minsertDataByFieldsFromListMap(tableName, listID, listFieldMapValue, e);
}

bool ZGSPRTDataMng::minsertDataByFieldFromList(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                       const ::std::string& fieldName, const ::ZG6000::StringList& listFieldValue, ZG6000::ErrorInfo& e)
{
    if (!m_initialized)
        return false;
    ZG6000::StringList fields;
    if (!getCachedTableFields(tableName, fields, e))
        return false;
    return false;
}

bool ZGSPRTDataMng::minsertDataByFieldsFromListMap(const ::std::string& tableName, const ::ZG6000::StringList& listID,
                                        const ::ZG6000::ListStringMap& listFieldMapValue, ZG6000::ErrorInfo& e)
{
    if (listID.empty() || listID.size() > 1000 || listID.size() != listFieldMapValue.size())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Invalid entry param.";
        return false;
    }
    return execRedisCommand([&](redisContext* context)->bool
    {
        ZG6000::StringMap fields;
        if (!getCachedTableFields(tableName, fields, e))
            return false;
        std::vector<const char*> commands;
        commands.resize(fields.size() * 2 + 2, "");
        commands[0] = "HMSET";
        size_t index = 1;
        for (const auto& fieldPair : fields)
        {
            commands[2 * index] = fieldPair.first.c_str();
            commands[2 * index + 1] = fieldPair.second.c_str();
            ++index;
        }
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writeStart(writer, tableName, "insert");
        for (size_t i = 0; i < listID.size(); ++i)
        {
            writeRowStart(writer, listID[i], "insert");
            writeInsertElem(writer, "id", listID[i].c_str());
            std::string key = tableName + "/" + listID[i];
            commands[1] = key.c_str();
            index = 1;
            for (const auto& fieldPair : fields)
            {
                if (fieldPair.first == "id")
                {
                    writeInsertElem(writer, fieldPair.first.c_str(), listID[i].c_str());
                    commands[2 * index + 1] = listID[i].c_str();
                }
                else
                {
                    const auto & fieldValue = listFieldMapValue[i].find(fieldPair.first);
                    if (fieldValue != listFieldMapValue[i].end())
                    {
                        writeInsertElem(writer, fieldValue->first.c_str(), fieldValue->second.c_str());
                        commands[2 * index + 1] = fieldValue->second.c_str();
                    }
                    else
                        writeInsertElem(writer, fieldPair.first.c_str(), fieldPair.second.c_str());
                }                
                ++index;
            }
            writer.EndObject();
            if (!doAppendRedisCommand(e, context, static_cast<int>(commands.size()), commands.data(), nullptr))
                return false;
        }
        writeEndElem(writer);
        for (size_t i = 0; i < listID.size(); ++i)
        {
            redisReply* reply = doGetRedisReply(e, context);
            if (reply == nullptr)
                return false;
            freeReplyObject(reply);
        }
        long long subscriberNum;
        std::string errMsg;
        m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//        redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//        freeReplyObject(pubReply);
        return true;
    }, e, true);
}

bool ZGSPRTDataMng::deleteDataByID(const ::std::string& tableName, const ::std::string& id, ZG6000::ErrorInfo& e)
{
    return execRedisCommand([&](redisContext* context)->bool
    {
        std::string key = tableName + "/" + id;
        redisReply* reply = doSendRedisCommand(e, context, "DEL %b", key.c_str(), key.size());
        if (reply == nullptr)
            return false;
        freeReplyObject(reply);
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writeStart(writer, tableName, "delete");
        writeRowStart(writer, id, "delete");
        writer.EndObject();
        writeEndElem(writer);
        long long subscriberNum;
        std::string errMsg;
        m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//        redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//        freeReplyObject(pubReply);
        return true;
    }, e, true);
}

bool ZGSPRTDataMng::mdeleteDataByID(const ::std::string& tableName, const ::ZG6000::StringList& listID, ZG6000::ErrorInfo& e)
{
    if (listID.empty() || listID.size() > m_maxRecordNum)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Invalid entry param.";
        return false;
    }
    return execRedisCommand([&](redisContext* context)->bool
    {
        std::vector<const char*> commands;
        commands.reserve(listID.size() + 1);
        commands.emplace_back("del");
        std::vector<std::string> keys;
        keys.reserve(listID.size());
        for (size_t i = 0; i < listID.size(); ++i)
        {
            std::string key = tableName + "/" + listID[i];
            keys.emplace_back(key);
            commands.emplace_back(keys[i].c_str());
        }
        redisReply* reply = doSendRedisCommand(e, context, static_cast<int>(commands.size()), commands.data(), nullptr);
        if (reply == nullptr)
            return false;
        freeReplyObject(reply);
        StringBuffer buffer;
        Writer<StringBuffer> writer(buffer);
        writeStart(writer, tableName, "delete");
        for (size_t i = 0; i < listID.size(); ++i)
        {
            writeRowStart(writer, listID[i], "delete");
            writer.EndObject();
        }
        writeEndElem(writer);
        long long subscriberNum;
        std::string errMsg;
        m_pRedisClient->rpush(tableName, buffer.GetString(), subscriberNum, errMsg);
//        redisReply* pubReply = doSendRedisCommand(e, context, "PUBLISH %b %b", tableName.data(), tableName.size(), buffer.GetString(), buffer.GetSize());
//        freeReplyObject(pubReply);
        return true;
    }, e, true);
}

bool ZGSPRTDataMng::deleteDataByField(const ::std::string&tableName, const ::std::string&id, const ::std::string&fieldName, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::deleteDataByFields(const ::std::string&tableName, const ::std::string&id, const ::ZG6000::StringList&listFieldName, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::mdeleteDataByField(const ::std::string&tableName, const ::ZG6000::StringList&listID, const ::std::string&fieldName, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::mdeleteDataByFields(const ::std::string&tableName, const ::ZG6000::StringList&listID,
                                        const ::ZG6000::StringList& listFieldName, ZG6000::ErrorInfo& e)
{
    return false;
}

bool ZGSPRTDataMng::test(const Ice::Current&)
{
    return false;
}

bool ZGSPRTDataMng::get()
{
    auto connection = m_pClient->connection();
    if (connection == nullptr)
        return false;
    redisContext* context = connection->context();
    redisReply* reply = static_cast<redisReply*>(redisCommand(context, "get name"));
    freeReplyObject(reply);
    return true;
}

ZGSPRTDataMng::ZGSPRTDataMng(QObject *parent) : QObject(parent)
{

}

bool ZGSPRTDataMng::tryConnectDatabase()
{
    m_selectedDb = 0;
    foreach (const DatabaseParams& params, m_listDbParams)
    {
        if ((getDatabaseType() != ZGRuntime::QMYSQL) && (getDatabaseType() != ZGRuntime::QODBC))
        {
            ZGLOG_ERROR("Database type error.");
            return false;
        }
        m_database->setConnectionParam(params.addr, params.name, params.port, params.user, params.password, params.timeout);
        ZGLOG_INFO(QString("connect to Database %1").arg(m_selectedDb));
        if (!m_database->connect())
        {
            m_selectedDb++;
            continue;
        }
        const auto connectionCount = std::thread::hardware_concurrency();
        for (unsigned int i = 0; i < connectionCount; ++i)
        {
            std::shared_ptr<ZGSPRTDatabase> conn;
            if (getDatabaseType() == ZGRuntime::QMYSQL)
                conn = std::make_shared<ZGSPRTMySQL>();
            else if (getDatabaseType() == ZGRuntime::QODBC)
                conn = std::make_shared<ZGSPRTSQLServer>();
            conn->setConnectionParam(params.addr, params.name, params.port, params.user, params.password, params.timeout);
            if (conn->connect())
                m_databasePool.add(conn);
        }
        return true;
    }
    return false;
}

bool ZGSPRTDataMng::tryCreateRedisClient()
{
    QString sql = "SELECT nodeID, aNetAddr, aNetPort, bNetAddr, bNetPort, password FROM sp_param_node_server WHERE serverTypeID = 'ZG_ST_REDIS_RT_DATA'";
    if (!m_database->execQuery(sql, [this](size_t num_fields, SQL_ROW& row)
    {
        if (row[0] == nullptr || row[1] == nullptr || row[2] == nullptr)
            return;
        ConnectionOption connectionOption;
        connectionOption.timeout = std::chrono::milliseconds(5000);
        QString nodeID = row[0];
        if (nodeID == m_localNodeID)
        {
            connectionOption.host = "127.0.0.1";
            connectionOption.port = std::atoi(row[2]);
            connectionOption.password = row[5];
            QString redisInfo = QString("Redis connection: host = %1, port = %2, password = %3")
                                    .arg(connectionOption.host.c_str()).arg(connectionOption.port)
                                    .arg(connectionOption.password.c_str());
            ZGLOG_INFO(redisInfo);
            m_connectionOptions.push_back(connectionOption);
            if (m_pRedisLocal == nullptr)
                m_pRedisLocal = new ZGRedisClient(m_connectionOptions);
            return;
        }
        else
        {
            connectionOption.host = row[1];
            if (connectionOption.host.empty())
                return;
            connectionOption.timeout = std::chrono::seconds(2);
            connectionOption.password = row[5];
            QString redisInfo = QString("Redis connection: host = %1, port = %2, password = %3")
                                    .arg(connectionOption.host.c_str()).arg(connectionOption.port)
                                    .arg(connectionOption.password.c_str());
            ZGLOG_INFO(redisInfo);
            m_connectionOptions.push_back(connectionOption);
            connectionOption.host = row[3];
            if (connectionOption.host.empty())
                return;
            connectionOption.port = std::atoi(row[4]);
            connectionOption.password = row[5];
            redisInfo = QString("Redis connection: host = %1, port = %2, password = %3")
                            .arg(connectionOption.host.c_str()).arg(connectionOption.port)
                            .arg(connectionOption.password.c_str());
            ZGLOG_INFO(redisInfo);
            m_connectionOptions.push_back(connectionOption);
        }
    }))
    {
        ZGLOG_ERROR(QString("execQuery error, sql = '%1'").arg(sql));
        return false;
    }
    if (m_connectionOptions.empty())
    {
        ZGLOG_ERROR("Can't find reallib server configuration.");
        return false;
    }
    m_pClient = std::make_shared<Redis>(m_connectionOptions, m_maxRtRedisConn);
    return true;
}

bool ZGSPRTDataMng::getRedisTopicClient()
{
    QList<ZGRuntime::REDIS_CLIENT_TYPE> listType;
    listType.append(ZGRuntime::REDIS_RT_QUEUE);
    if (!ZGRuntime::instance()->initRedisClient(listType, static_cast<int>(m_maxTopicRedisConn)))
    {
        ZGLOG_ERROR("initRedisClient fail.");
        return false;
    }
    m_pRedisClient = ZGRuntime::instance()->getRedisClientRTQueue();
    if (m_pRedisClient == nullptr)
    {
        ZGLOG_ERROR("getRedisClientTopic fail.");
        return false;
    }
    return true;
}

bool ZGSPRTDataMng::isClientConnected()
{
    return m_pClient->connected(true);
}

bool ZGSPRTDataMng::initDatabaseParams()
{
    QMap<QString, QString> mapCfgParam;
    QList<QMap<QString, QString>> listMapParam;
    if (!ZGPubFun::getParamCfgDB(mapCfgParam, listMapParam))
    {
        ZGLOG_ERROR("ZGPubFun::getParamCfgDB error.");
        return false;
    }
    if (listMapParam.empty())
    {
        ZGLOG_ERROR("Can't find any database config param.");
        return false;
    }
    for (const auto& mapParam : listMapParam)
    {
        for (const QString& key : mapParam.keys())
        {
            std::cout << key.toStdString() << " ";
        }
        std::cout << "\n";
    }
    unsigned int dbNetNum = getMapValue(mapCfgParam, "db_net_num", QString::number(listMapParam.size())).toInt();
    unsigned int encrypt = getMapValue(mapCfgParam, "db_password_encrypt", 0).toInt();
    m_listDbParams.clear();
    DatabaseParams dbParams;
    QString dbInfo;
    unsigned int index = 0;
    for (const QMap<QString, QString>& mapParam: listMapParam)
    {
        dbParams.name = getMapValue(mapParam, QString("db_%1_dbname").arg(index), "zg6000");
        dbParams.addr = getMapValue(mapParam, QString("db_%1_ip").arg(index), "127.0.0.1");
        dbParams.port = getMapValue(mapParam, QString("db_%1_port").arg(index), QString::number(3306)).toInt();
        dbParams.user = getMapValue(mapParam, QString("db_%1_user").arg(index), "root");
        QString originPassword = getMapValue(mapParam, QString("db_%1_password").arg(index), "zg123456");
        if (encrypt == 1)
            dbParams.password = ZGPubFun::aesDecrypt(originPassword);
        else
            dbParams.password = originPassword;
        dbParams.timeout = getMapValue(mapParam, QString("db_%1_connect_timeout").arg(index), QString::number(15)).toInt();
        dbInfo = QString("database %1 name = %2, ip = %3, port = %4, user = %5, password = %6, connect timeout = %7").arg(index)
                            .arg(dbParams.name).arg(dbParams.addr).arg(dbParams.port).arg(dbParams.user).arg(dbParams.password)
                            .arg(dbParams.timeout);
        ZGLOG_INFO(dbInfo);
        m_listDbParams.append(dbParams);
        ++index;
        if (index >= dbNetNum)
            break;
    }
    return true;
}

bool ZGSPRTDataMng::isDefaultMaster()
{
    QString sql = QString("SELECT defaultMaster FROM sp_param_node_service_instance WHERE id = '%1';").arg(m_instName);
    bool isDefaultMaster = false;
    if (!m_database->execQuery(sql, [&isDefaultMaster](size_t num_fields, SQL_ROW& row)
	{
        if (row[0] == nullptr)
            return;
        std::string defaultMaster = row[0];
        isDefaultMaster = (defaultMaster == "1");
    }))
        return false;
    return isDefaultMaster;
}

bool ZGSPRTDataMng::checkMasterOnline()
{
    return true;
}

bool ZGSPRTDataMng::isFirstStart(bool& firstStart)
{
    auto connection = m_pClient->connection();
    if (connection == nullptr)
        return false;
    try
    {
        auto str = connection->get("zg6000");
        firstStart = !str;
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGSPRTDataMng::flushData()
{
    auto connection = m_pClient->connection(true);
    if (connection == nullptr)
        return false;
    try
    {
        connection->flushall();
        return true;
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGSPRTDataMng::setCacheFlag()
{
    auto connection = m_pClient->connection(true);
    if (connection == nullptr)
        return false;
    try
    {
        return connection->set("zg6000", "1");
    }
    catch (const std::exception& e)
    {
        ZGLOG_ERROR(e.what());
        return false;
    }
}

bool ZGSPRTDataMng::syncTables()
{
    std::chrono::system_clock::time_point start = std::chrono::system_clock::now();
    std::vector<std::string> tableNames;
    if (!m_database->getTables(tableNames))
    {
        ZGLOG_ERROR("Get tables name error.");
        return false;
    }
    ZGLOG_INFO(QString("tableNames size: %1").arg(tableNames.size()));
    auto connection = m_pClient->connection(true);
    if (!connection)
    {
        ZGLOG_ERROR("Can't get master redis connection.");
        return false;
    }
    QMap<std::string, QFuture<bool>> mapTask;
    for (const std::string& tableName : tableNames)
    {
        mapTask[tableName] = QtConcurrent::run( &ZGSPRTDataMng::importFromTable, this, tableName);
    }
    if (!mapTask.empty())
    {
        auto task = mapTask.begin();
        while (task != mapTask.end())
        {
            bool result = task.value().result();
            if (!result)
            {
                ZGLOG_ERROR(QString("Import table %1 error.").arg(task.key().c_str()));
            }
            ++task;
        }
    }
    std::chrono::system_clock::time_point end = std::chrono::system_clock::now();
    int64_t duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
    ZGLOG_INFO(QString("import database finished, duration = %1 ms.").arg(duration));
    QString sql;
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
        sql = "TRUNCATE TABLE sp_maintain_records";
    else
        sql = "DELETE FROM sp_maintain_records";
    m_database->execCommand(sql);
    return true;
}

bool ZGSPRTDataMng::syncFields()
{
    QElapsedTimer timer;
    timer.start();
    ZG6000::StringList tableNames;
    if (!m_database->getTables(tableNames))
    {
        ZGLOG_ERROR("Get tables name error.");
        return false;
    }
    ZG6000::MapStringMap tableFields;
    if (ZGRuntime::instance()->getDBType() == ZGRuntime::QMYSQL)
    {
        for (const std::string& tableName : tableNames)
        {
            ZG6000::StringMap fields;
            if (!m_database->getTableFields(tableName, fields))
            {
                ZGLOG_ERROR(QString("getTableFields error, tableName = '%1'").arg(tableName.c_str()));
                return false;
            }
            tableFields[tableName] = fields;
        }
    }
    else
    {
        QString sql = "SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS;";
        if (!m_database->execQuery(sql, [&](size_t num_fields, SQL_ROW& row)
        {
            std::string tableName = row[0];
            std::string columnName = row[1];
            std::string dataType = row[2];
            auto it = std::find(tableNames.begin(), tableNames.end(), tableName);
            if (it == tableNames.end())
                return;
            else
            {
                tableFields[tableName][columnName] = dataType;
            }
        }))
        {
            ZGLOG_ERROR(QString("execQuery error, sql = '%1'").arg(sql));
            return false;
        }
        ZGLOG_TRACE(QString("tableFields size: %1").arg(tableFields.size()));
    }
    std::unique_lock locker(m_mutex);
    m_tablesFields = tableFields;
    ZGLOG_INFO(QString("syncFields finished, duration = %1 ms").arg(timer.elapsed()));
    return true;
}

bool ZGSPRTDataMng::syncData()
{
    bool firstStart = true;
    if (!isFirstStart(firstStart))
    {
        ZGLOG_ERROR("can't get first start flag.");
        return false;
    }
    if (firstStart)
    {
        ZGLOG_INFO("first start");
        if (!syncTables())
        {
            ZGLOG_ERROR("syncTables error.");
            return false;
        }
        if (!setCacheFlag())
        {
            ZGLOG_ERROR("setCacheFlag error.");
            return false;
        }
    }
    return true;
}

bool ZGSPRTDataMng::syncTableFields(ZGSPRTDatabase& database,
                                    const std::string& tableName, ZG6000::StringList& fieldsName)
{
    ZG6000::StringMap fields;
    if (!database.getTableFields(tableName, fields))
    {
        ZGLOG_ERROR(QString("getTableFields error, tableName = '%1'").arg(tableName.c_str()));
        return false;
    }
    addTableFields(tableName, fields);
    if (!database.getTableFields(tableName, fieldsName))
    {
        ZGLOG_ERROR(QString("getTableFields error, tableName = '%1'").arg(tableName.c_str()));
        return false;
    }
    // try
    // {
    //     std::string key = tableName + "$fields";
    //     connection->del(key);
    //     if (!database.getTableFields(tableName, fieldsName))
    //         return false;
    //     connection->rpush(key, fieldsName.begin(), fieldsName.end());
    // }
    // catch (std::exception& e)
    // {
    //     ZGLOG_ERROR(e.what());
    //     return false;
    // }
    return true;
}

bool ZGSPRTDataMng::importFromTable(const std::string& tableName)
{
    auto database = m_databasePool.instance();
    QString sql;
    // TODO: 判断数据库类型
    if (getDatabaseType() == ZGRuntime::QMYSQL)
    {
        sql = "set names utf8";
        if (!database->execCommand(sql))
        {
            ZGLOG_ERROR(QString("execCommand error, sql = '%1'").arg(sql));
            return false;
        }
    }
    auto connection = m_pClient->connection(true);
    if (!connection)
    {
        ZGLOG_ERROR("Can't get redis connection.");
        return false;
    }
    ZG6000::StringList fieldsName;
    if (!database->getTableFields(tableName, fieldsName))
    {
        ZGLOG_ERROR(QString("getTableFields error, tableName = '%1'").arg(tableName.c_str()));
        return false;
    }
    sql = QString("select * from %1").arg(tableName.c_str());
    const char* paramArgs[MaxFieldSize];
    paramArgs[0] = "hmset";
    size_t fieldNum = fieldsName.size();
    for (size_t i = 0; i < fieldNum; ++i)
    {
        paramArgs[i * 2 + 2] = fieldsName[i].c_str();
    }
    redisContext* context = connection->context();
    if (!_check_ctx(context))
    {
        ZGLOG_ERROR("Check redis context error.");
        return false;
    }
    int batchSize = 0;
    std::string strPrefix = tableName + "/";
    if (!database->execQuery(sql, [&paramArgs, &batchSize, &strPrefix, fieldNum, context](size_t num_fields, SQL_ROW& row)
    {
        std::string key = strPrefix + row[0];
        paramArgs[1] = key.c_str();
        for (size_t i = 0; i < fieldNum; ++i)
        {
            if (row[i] != nullptr)
                paramArgs[i * 2 + 3] = row[i];
            else
                paramArgs[i * 2 + 3] = "";
        }
        redisAppendCommandArgv(context, static_cast<int>(fieldNum) * 2 + 2, paramArgs, nullptr);
        ++batchSize;
        if (batchSize >= 1000)
        {
            commitMultiCommand(context, batchSize);
        }
    }))
    {
        ZGLOG_ERROR(QString("execQuery error, sql = '%1'").arg(sql));
        return false;
    }
    if (batchSize > 0)
        commitMultiCommand(context, batchSize);
    return true;
}

bool ZGSPRTDataMng::checkContext(redisContext* context, ZG6000::ErrorInfo& e)
{
    if (context == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_EXEC);
        e.errReason = "Redis context is null.";
        return false;
    }
    if (context->err != 0)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_EXEC);
        const char* errstr = context->errstr;
        if (errstr)
            e.errReason = "code: " + std::to_string(context->err) + ", " + errstr;
        else
            e.errReason = "context unknown error, code: " + std::to_string(context->err);
        return false;
    }
    return (context != nullptr && context->err == 0);
}

bool ZGSPRTDataMng::checkReply(redisReply* reply, ZG6000::ErrorInfo& e)
{
    if (reply == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
        e.errReason = "Redis reply is null.";
        return false;
    }
    if (reply->type == REDIS_REPLY_ERROR)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
        if (reply->str)
            e.errReason = reply->str;
        else
            e.errReason = "Reply unknown error.";
        freeReplyObject(reply);
        return false;
    }
    return true;
}

void ZGSPRTDataMng::commitMultiCommand(redisContext* ctx, int& commandNum)
{
    while (commandNum)
    {
        redisReply* reply;
        redisGetReply(ctx, reinterpret_cast<void**>(&reply));
        freeReplyObject(reply);
        commandNum--;
    }
}

bool ZGSPRTDataMng::execRedisCommand(std::function<bool(redisContext*)> func, ZG6000::ErrorInfo& e, bool master)
{
    if (!m_initialized)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_SERVER_INIT);
        e.errDetail = "server is not initialized.";
        ZGLOG_ERROR(e);
        return false;
    }
    auto connection = m_pClient->connection(master);
    if (connection == nullptr)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_CONN);
        e.errDetail = "Can't get an available redis connection";
        return false;
    }
    redisContext* context = connection->context();
    if (!checkContext(context, e))
    {
        e.errDetail = "Check redis context error.";
        return false;
    }
    if (func)
        return func(context);
    return true;
}

redisReply* ZGSPRTDataMng::doSendRedisCommand(ZG6000::ErrorInfo& e, redisContext* c, int argc, const char** argv,
    const size_t* argvlen)
{
    redisReply* reply = static_cast<redisReply*>(redisCommandArgv(c, argc, argv, argvlen));
    if (!checkReply(reply, e))
        return nullptr;
    return reply;
}

redisReply* ZGSPRTDataMng::doSendRedisCommand(ZG6000::ErrorInfo& e, redisContext* c, const char* format, ...)
{
    va_list args;
    va_start(args, format);
    redisReply* reply = static_cast<redisReply*>(redisvCommand(c, format, args));
    va_end(args);
    if (!checkReply(reply, e))
        return nullptr;
    return reply;
}

bool ZGSPRTDataMng::doAppendRedisCommand(ZG6000::ErrorInfo& e, redisContext* c, int argc, const char** argv,
    const size_t* argvlen)
{
    if (redisAppendCommandArgv(c, argc, argv, argvlen) != REDIS_OK)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_EXEC);
        e.errDetail = "Append redis command error.";
        return false;
    }
    return true;
}

bool ZGSPRTDataMng::doAppendRedisCommand(ZG6000::ErrorInfo& e, redisContext* c, const char* format, ...)
{
    va_list args;
    va_start(args, format);
    int result = redisvAppendCommand(c, format, args);
    va_end(args);
    if (result != REDIS_OK)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_EXEC);
        e.errDetail = "Append redis command error.";
        return false;
    }
    return true;
}

redisReply* ZGSPRTDataMng::doGetRedisReply(ZG6000::ErrorInfo& e, redisContext* c)
{
    redisReply *reply{nullptr};
    if (redisGetReply(c, reinterpret_cast<void**>(&reply)) != REDIS_OK)
    {
        freeReplyObject(reply);
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
        e.errDetail = "Get redis reply error.";
        return nullptr;
    }
    if (!checkReply(reply, e))
        return nullptr;
    return reply;
}

bool ZGSPRTDataMng::multiReplyToStringList(redisContext* context, size_t replyNum, ZG6000::StringList& stringList, ZG6000::ErrorInfo& e)
{
    stringList.reserve(replyNum);
    for (size_t i = 0; i < replyNum; ++i)
    {
        redisReply* reply = doGetRedisReply(e, context);
        if (reply == nullptr)
            return false;
        if (reply->type == REDIS_REPLY_NIL)
            stringList.emplace_back("");
        else
            stringList.emplace_back(reply->str);
        freeReplyObject(reply);
    }
    return true;
}

bool ZGSPRTDataMng::replyArrayToStringList(redisReply* reply, ZG6000::StringList& stringList, ZG6000::ErrorInfo& e)
{
    if (reply->type != REDIS_REPLY_ARRAY)
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
        e.errDetail = "Redis reply error, expect array reply.";
        return false;
    }
    stringList.reserve(reply->elements);
    for (size_t i = 0; i < reply->elements; ++i)
    {
        if (reply->element[i]->type == REDIS_REPLY_NIL)
                stringList.emplace_back("");
            else
                stringList.emplace_back(reply->element[i]->str);
    }
    return true;
}

void ZGSPRTDataMng::addTableFields(const std::string& tableName, const ZG6000::StringMap& fields)
{
    std::unique_lock locker(m_mutex);
    m_tablesFields.insert(std::make_pair(tableName, fields));
}

ZGRuntime::DB_TYPE ZGSPRTDataMng::getDatabaseType()
{
    return ZGRuntime::instance()->getDBType();
}

bool ZGSPRTDataMng::_check_ctx(redisContext *ctx)
{
    if (ctx == nullptr)
    {
        ZGLOG_ERROR("context is null.");
        return false;
    }
    if (ctx->err != 0)
    {
        const char* errstr = ctx->errstr;
        if (errstr)
            ZGLOG_ERROR(QString("%1, code: %2").arg(ctx->err).arg(errstr));
        else
            ZGLOG_ERROR("context unknown error, errorcode = %d", ctx->err);
        return false;
    }
    return (ctx != nullptr && ctx->err == 0);
}

bool ZGSPRTDataMng::_check_reply(redisReply *reply)
{
    if (reply == nullptr)
    {
        ZGLOG_ERROR("reply is null.");
        return false;
    }
    if (reply->type == REDIS_REPLY_ERROR)
    {
        if (reply->str)
            ZGLOG_ERROR( reply->str);
        else
            ZGLOG_ERROR("reply unknown error.");
        return false;
    }
    return true;
}

bool ZGSPRTDataMng::getCachedTableFields(const std::string tableName, ZG6000::StringList &fields, ZG6000::ErrorInfo& e)
{
    std::shared_lock locker(m_mutex);
    const auto & tablePair = m_tablesFields.find(tableName);
    if (tablePair == m_tablesFields.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Can't find table " + tableName;
        return false;
    }
    for (const auto& fieldPair : tablePair->second)
    {
        fields.push_back(fieldPair.first);
    }
    return true;
}

bool ZGSPRTDataMng::getCachedTableFields(const std::string tableName, ZG6000::StringMap& fields, ZG6000::ErrorInfo& e)
{
    std::shared_lock locker(m_mutex);
    const auto & tablePair = m_tablesFields.find(tableName);
    if (tablePair == m_tablesFields.end())
    {
        e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_INPUT_PARAM);
        e.errDetail = "Can't find table " + tableName;
        return false;
    }
    fields = tablePair->second;
    return true;
}

QString ZGSPRTDataMng::getMapValue(const QMap<QString, QString>& mapParam, const QString& key, QString defaultValue)
{
    auto it = mapParam.find(key);
    if (it != mapParam.constEnd())
        return it.value();
    ZGLOG_WARN(QString("Can't find key '%1', use default value '%2' instead.").arg(key).arg(defaultValue));
    return defaultValue;
}

void ZGSPRTDataMng::getMapListKeys(const ZG6000::ListStringMap& listFieldMapValue,
                                   std::vector<std::vector<const char *> > &listFieldNames, std::vector<std::vector<const char *> > &listFieldValues)
{
    listFieldNames.reserve(listFieldMapValue.size());
    listFieldValues.reserve(listFieldMapValue.size());
    for (auto it = listFieldMapValue.begin(); it != listFieldMapValue.end(); ++it)
    {
        std::vector<const char*> fieldNames, fieldValues;
        splitMap(*it, fieldNames, fieldValues);
        listFieldNames.emplace_back(std::move(fieldNames));
        listFieldValues.emplace_back(std::move(fieldValues));
    }
}

void ZGSPRTDataMng::splitMap(const ZG6000::StringMap& keyValue, std::vector<const char*>& keys, std::vector<const char*>& values)
{
    keys.reserve(keyValue.size());
    values.reserve(keyValue.size());
    for (const auto& it : keyValue)
    {
        keys.push_back(it.first.c_str());
        values.push_back(it.second.c_str());
    }
}

bool ZGSPRTDataMng::appendMultiCommand(redisContext* context, const std::string& tableName, const std::string& id,
                                          const ZG6000::StringMap& fieldMapValue)
{
    if (fieldMapValue.empty())
    {
        ZGLOG_ERROR(QString("table name %1 id %2 has no elements").arg(tableName.c_str()).arg(id.c_str()));
        return false;
    }
    std::string key = tableName + "/" + id;
    std::vector<const char*> vCommand;
    vCommand.reserve(fieldMapValue.size() + 2);
    vCommand.emplace_back("hmget");
    vCommand.emplace_back(key.c_str());
    for (const auto & it : fieldMapValue)
    {
        vCommand.emplace_back(it.first.c_str());
    }
    if (redisAppendCommandArgv(context, static_cast<int>(vCommand.size()), vCommand.data(), nullptr) != REDIS_OK)
    {
        ZGLOG_ERROR("redisAppendCommandArgv error.");
        return false;
    }
    vCommand.clear();
    vCommand.reserve(fieldMapValue.size() * 2 + 2);
    vCommand.emplace_back("hmset");
    vCommand.emplace_back(key.c_str());
    for (const auto & it : fieldMapValue)
    {
        vCommand.emplace_back(it.first.c_str());
        vCommand.emplace_back(it.second.c_str());
    }
    if (redisAppendCommandArgv(context, static_cast<int>(vCommand.size()), vCommand.data(), nullptr) != REDIS_OK)
    {
        ZGLOG_ERROR("redisAppendCommandArgv error.");
        return false;
    }
    return true;
}

bool ZGSPRTDataMng::appendMultiCommand(redisContext* context, const std::string& tableName, const std::string& id,
    std::vector<const char*>& commandsGet, std::vector<const char*>& commandsSet, const ZG6000::StringList& fieldValues)
{
    if ((fieldValues.size() + 2) != commandsGet.size())
    {
        ZGLOG_ERROR("field name size does not match field value size.");
        return false;
    }
    std::string key = tableName + "/" + id;
    commandsGet[1] = key.c_str();
    if (redisAppendCommandArgv(context, static_cast<int>(commandsGet.size()), commandsGet.data(), nullptr) != REDIS_OK)
    {
        ZGLOG_ERROR("redisAppendCommandArgv error.");
        return false;
    }
    commandsSet[1] = key.c_str();
    for (size_t i = 0; i < fieldValues.size(); ++i)
    {
        commandsSet[i * 2 + 3] = fieldValues[i].c_str();
    }
    if (redisAppendCommandArgv(context, static_cast<int>(commandsSet.size()), commandsSet.data(), nullptr) != REDIS_OK)
    {
        ZGLOG_ERROR("redisAppendCommandArgv error.");
        return false;
    }
    return true;
}

bool ZGSPRTDataMng::appendMultiCommand(redisContext* context, const std::string& tableName, const std::string& id, const std::string& fieldName, const std::string& fieldValue)
{
    std::string key = tableName + "/" + id;
    if (redisAppendCommand(context, "hget %b %b", key.data(), key.size(), fieldName.data(), fieldName.size()) != REDIS_OK)
    {
        ZGLOG_ERROR("redisAppendCommandArgv error.");
        return false;
    }
    if (redisAppendCommand(context, "hset %b %b %b", key.data(), key.size(), fieldName.data(), fieldName.size(), fieldValue.data(), fieldValue.size()) != REDIS_OK)
    {
        ZGLOG_ERROR("redisAppendCommandArgv error.");
        return false;
    }
    return true;
}

bool ZGSPRTDataMng::verifyQueuedCommand(redisContext* context, size_t commandNum, ZG6000::ErrorInfo& e)
{
    bool success = true;
    for (size_t i = 0; i < commandNum; ++i)
    {
        redisReply* reply = doGetRedisReply(e, context);
        if (reply == nullptr)
            return false;
        if ((reply->type != REDIS_REPLY_STATUS) || (strcmp(reply->str, "QUEUED") != 0))
        {
            e = ZGRuntime::instance()->getErrorInfo(ZGSPRTData::ZG_ERR_REDIS_REPLY);
            e.errDetail = "Reply type mismatch.";
            success = false;
        }
        freeReplyObject(reply);
    }
    return success;
}

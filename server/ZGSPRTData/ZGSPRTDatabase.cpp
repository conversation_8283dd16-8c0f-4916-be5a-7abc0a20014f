#include "ZGSPRTDatabase.h"
#include <QDebug>

ZGSPRTDatabase::ZGSPRTDatabase(QObject* parent): QObject(parent)
{

}

ZGSPRTDatabase::~ZGSPRTDatabase()
{
}

void ZGSPRTDatabase::setConnectionParam(const QString& host, const QString& database, unsigned port,
    const QString& userName, const QString& password, unsigned timeout)
{
    m_host = host;
    m_database = database;
    m_port = port;
    m_userName = userName;
    m_password = password;
    m_timeout = timeout;
}

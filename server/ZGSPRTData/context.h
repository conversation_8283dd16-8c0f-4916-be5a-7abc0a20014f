#pragma once

#include "thirdparty/hiredis/hiredis.h"
#include <cstdlib>
#include <string>
#include <deque>
#include <memory>
#include <functional>
#include <mutex>
#include <QDebug>

class RedisContext
{
public:
    RedisContext(const std::string& host, int port = 6379, int timeout = 1, const std::string& password = ""): host_(host), port_(port), password_(password)
    {
        tv_.tv_sec = 0;
        tv_.tv_usec = timeout * 1000;
    }
    bool initContext(size_t num = 4)
    {
        if (num > 20)
        {
            qDebug() << "Invalid context num.";
            return false;
        }
        for (std::size_t i = 0; i < num; i++)
        {
            redisContext* context = redisConnectWithTimeout(host_.c_str(), port_, tv_);
            redisSetTimeout(context, tv_);
            context_.push_back(std::unique_ptr<redisContext>(context));
        }
        return true;
    }

    std::unique_ptr<redisContext, std::function<void(redisContext*)>> acquire()
    {
        std::unique_lock<std::mutex> lock(mutex_);
        if (context_.empty())
            return nullptr;
        redisContext* ctx = context_.front().release();
        context_.pop_front();
        if (ctx == nullptr)
            ctx = redisConnectWithTimeout(host_.c_str(), port_, tv_);
        if (ctx->err)
        {
            redisFree(ctx);
            ctx = redisConnectWithTimeout(host_.c_str(), port_, tv_);
        }
        std::unique_ptr<redisContext, std::function<void(redisContext*)>> ptr(ctx, [this](redisContext* context)
            {
                std::unique_lock<std::mutex> lock(mutex_);
                context_.push_back(std::unique_ptr<redisContext>(context));
            });
        return ptr;
    }

private:
    std::string host_;
    int port_;
    std::string password_;
    std::deque<std::unique_ptr<redisContext>> context_;
    timeval tv_;
    std::mutex mutex_;
};

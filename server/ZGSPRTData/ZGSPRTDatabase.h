#pragma once
#include <QObject>
#include <map>

using SQL_ROW = char**;
class ZGSPRTDatabase : public QObject
{
public:
    ZGSPRTDatabase(QObject* parent = nullptr);
    virtual ~ZGSPRTDatabase();
    void setConnectionParam(const QString& host, const QString& database, unsigned int port,
                    const QString& userName, const QString& password, unsigned int timeout = 15);
    virtual bool connect() = 0;
    virtual void close() = 0;
    using FuncRow = std::function<void(size_t num_fields, SQL_ROW& row)>;
    virtual bool execQuery(const QString& sql, const FuncRow& processRow = nullptr) = 0;
    using FuncResult = std::function<void(unsigned long long affectedRows)>;
    virtual bool execCommand(const QString& sql, const FuncResult& processResult = nullptr) = 0;
    virtual bool getTables(std::vector<std::string>& tables) = 0;
    virtual bool getTableFields(const std::string& tableName, std::vector<std::string> &fieldsName) = 0;
    virtual bool getTableFields(const QString& tableName, QStringList& fieldsName) = 0;
    virtual bool getTableFields(const std::string& tableName, std::map<std::string, std::string>& fields) = 0;
protected:
    QString m_host;
    QString m_database;
    unsigned int m_port;
    QString m_userName;
    QString m_password;
    unsigned int m_timeout;
};


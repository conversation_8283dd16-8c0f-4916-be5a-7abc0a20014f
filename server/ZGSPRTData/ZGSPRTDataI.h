#ifndef __ZGSPRTDataI_h__
#define __ZGSPRTDataI_h__

#include <ZGSPRTData.h>

namespace ZG6000
{

class ZGSPRTDataI : public virtual ZGSPRTData
{
public:
    ZGSPRTDataI();

    bool checkState(const Ice::Current&) override;

    bool getDataByKeyToValue(std::string,
                      std::string&,
                      ErrorInfo&,
                      const Ice::Current&) override;

    bool getDataByKeyToJson(std::string,
                            std::string&,
                            ErrorInfo&,
                            const Ice::Current&) override;

    bool setDataByKeyFromValue(std::string,
                      std::string,
                      ErrorInfo&,
                      const Ice::Current&) override;

    void setDataByKeyFromValueOneway(::std::string,
                                             ::std::string,
                                             const Ice::Current&) override;

    bool mgetDataByKeyToList(StringList,
                       StringList&,
                       ErrorInfo&,
                       const Ice::Current&) override;

    bool mgetDataByKeyToMap(StringList,
                            StringMap&,
                            ErrorInfo&,
                            const Ice::Current&) override;

    bool mgetDataByKeyToJson(StringList,
                             std::string&,
                             ErrorInfo&,
                             const Ice::Current&) override;

    bool msetDataByKeyFromList(StringList,
                       StringList,
                       ErrorInfo&,
                       const Ice::Current&) override;

    void msetDataByKeyFromListOneway(StringList,
                                             StringList,
                                             const Ice::Current&) override;

    bool msetDataByKeyFromMap(StringMap,
                      ErrorInfo&,
                      const Ice::Current&) override;

    void msetDataByKeyFromMapOneway(StringMap,
                                            const Ice::Current&) override;
    
    bool getDataByIDToMap(std::string,
                          std::string,
                          StringMap&,
                          ErrorInfo&,
                          const Ice::Current&) override;

    bool getDataByIDToJson(std::string,
                           std::string,
                           std::string&,
                           ErrorInfo&,
                           const Ice::Current&) override;

    bool getDataByFieldToValue(std::string,
                        std::string,
                        std::string,
                        std::string&,
                        ErrorInfo&,
                        const Ice::Current&) override;

    bool getDataByFieldToJson(std::string,
                              std::string,
                              std::string,
                              std::string&,
                              ErrorInfo&,
                              const Ice::Current&) override;

    bool getDataByFieldsToList(std::string,
                         std::string,
                         StringList,
                         StringList&,
                         ErrorInfo&,
                         const Ice::Current&) override;

    bool getDataByFieldsToMap(::std::string,
                        ::std::string,
                        StringList,
                        StringMap&,
                        ErrorInfo&,
                        const Ice::Current&) override;

    bool getDataByFieldsToJson(std::string,
                               std::string,
                               StringList,
                               std::string&,
                               ErrorInfo&,
                               const Ice::Current&) override;

    bool mgetDataByIDToListList(std::string,
                       StringList,
                       ListStringList&,
                       ErrorInfo&,
                       const Ice::Current&) override;

    bool mgetDataByIDToJson(std::string,
                             StringList,
                             std::string&,
                             ErrorInfo&,
                             const Ice::Current&) override;

    bool mgetDataByIDToListMap(std::string,
                          StringList,
                          ListStringMap&,
                          ErrorInfo&,
                          const Ice::Current&) override;

    bool mgetDataByIDToMapMap(::std::string,
                         StringList,
                         MapStringMap&,
                         ErrorInfo&,
                         const Ice::Current&) override;

    bool mgetDataByFieldToList(std::string,
                         StringList,
                         std::string,
                         StringList&,
                         ErrorInfo&,
                         const Ice::Current&) override;

    bool mgetDataByFieldToMap(::std::string,
                             StringList,
                             ::std::string,
                             StringMap&,
                             ErrorInfo&,
                             const Ice::Current&) override;

    bool mgetDataByFieldToJson(std::string,
                               StringList,
                               std::string,
                               std::string&,
                               ErrorInfo&,
                               const Ice::Current&) override;

    bool mgetDataByFieldsToListList(std::string,
                          StringList,
                          StringList,
                          ListStringList&,
                          ErrorInfo&,
                          const Ice::Current&) override;

    bool mgetDataByFieldsToJson(std::string,
                                StringList,
                                StringList,
                                std::string&,
                                ErrorInfo&,
                                const Ice::Current&) override;

    bool mgetDataByFieldsToListMap(std::string,
                             StringList,
                             StringList,
                             ListStringMap&,
                             ErrorInfo&,
                             const Ice::Current&) override;

    bool mgetDataByFieldsToMapMap(::std::string,
                            StringList,
                            StringList,
                            MapStringMap&,
                            ErrorInfo&,
                            const Ice::Current&) override;

    bool updateDataByFieldFromValue(std::string,
                           std::string,
                           std::string,
                           std::string,
                           ErrorInfo&,
                           const Ice::Current&) override;

    void updateDataByFieldFromValueOneway(::std::string,
                                          ::std::string,
                                          ::std::string,
                                          ::std::string,
                                          const Ice::Current&) override;

    bool updateDataByIDFromMap(::std::string,
                               ::std::string,
                               StringMap,
                               ErrorInfo&,
                               const Ice::Current&) override;

    void updateDataByIDFromMapOneway(::std::string,
                                     ::std::string,
                                     StringMap,
                                     const Ice::Current&) override;

    bool mupdateDataByFieldFromList(::std::string,
                                    StringList,
                                    ::std::string,
                                    StringList,
                                    ErrorInfo&,
                                    const Ice::Current&) override;

    void mupdateDataByFieldFromListOneway(::std::string,
                                          StringList,
                                          ::std::string,
                                          StringList,
                                          const Ice::Current&) override;

    bool mupdateDataByFieldFromValue(::std::string,
                                     StringList,
                                     ::std::string,
                                     ::std::string,
                                     ErrorInfo&,
                                     const Ice::Current&) override;

    void mupdateDataByFieldFromValueOneway(::std::string,
                                           StringList,
                                           ::std::string,
                                           ::std::string,
                                           const Ice::Current&) override;

    bool mupdateValueByFieldsFromListList(::std::string,
                                          StringList,
                                          StringList,
                                          ListStringList,
                                          ErrorInfo&,
                                          const Ice::Current&) override;

    void mupdateValueByFieldsFromListListOneway(::std::string,
                                                StringList,
                                                StringList,
                                                ListStringList,
                                                const Ice::Current&) override;

    bool mupdateDataByFieldsFromListMap(::std::string,
                                        StringList,
                                        ListStringMap,
                                        ErrorInfo&,
                                        const Ice::Current&) override;

    void mupdateDataByFieldsFromListMapOneway(::std::string,
                                              StringList,
                                              ListStringMap,
                                              const Ice::Current&) override;

    bool mupdateDataByFieldsFromMap(::std::string,
                                    StringList,
                                    StringMap,
                                    ErrorInfo&,
                                    const Ice::Current&) override;

    void mupdateDataByFieldsFromMapOneway(::std::string,
                                          StringList,
                                          StringMap,
                                          const Ice::Current&) override;

    bool updateDataID(::std::string,
                      ::std::string,
                      ::std::string,
                      ErrorInfo&,
                      const Ice::Current&) override;

    void updateDataIDOneway(::std::string,
                            ::std::string,
                            ::std::string,
                            const Ice::Current&) override;

    bool mupdateDataID(::std::string,
                       StringList,
                       StringList,
                       ErrorInfo&,
                       const Ice::Current&) override;

    void mupdateDataIDOneway(::std::string,
                             StringList,
                             StringList,
                             const Ice::Current&) override;

    bool insertDataByFieldFromValue(::std::string,
                                    ::std::string,
                                    ::std::string,
                                    ::std::string,
                                    ErrorInfo&,
                                    const Ice::Current&) override;

    void insertDataByFieldFromValueOneway(::std::string,
                                          ::std::string,
                                          ::std::string,
                                          ::std::string,
                                          const Ice::Current&) override;

    bool insertDataByIDFromMap(::std::string,
                               ::std::string,
                               StringMap,
                               ErrorInfo&,
                               const Ice::Current&) override;

    void insertDataByIDFromMapOneway(::std::string,
                                     ::std::string,
                                     StringMap,
                                     const Ice::Current&) override;

    bool minsertDataByFieldFromList(::std::string,
                                    StringList,
                                    ::std::string,
                                    StringList,
                                    ErrorInfo&,
                                    const Ice::Current&) override;

    void minsertDataByFieldFromListOneway(::std::string,
                                          StringList,
                                          ::std::string,
                                          StringList,
                                          const Ice::Current&) override;

    bool minsertDataByFieldsFromListMap(::std::string,
                                        StringList,
                                        ListStringMap,
                                        ErrorInfo&,
                                        const Ice::Current&) override;

    void minsertDataByFieldsFromListMapOneway(::std::string,
                                              StringList,
                                              ListStringMap,
                                              const Ice::Current&) override;

    bool deleteDataByID(::std::string,
                        ::std::string,
                        ErrorInfo&,
                        const Ice::Current&) override;

    void deleteDataByIDOneway(::std::string,
                              ::std::string,
                              const Ice::Current&) override;

    bool mdeleteDataByID(::std::string,
                         StringList,
                         ErrorInfo&,
                         const Ice::Current&) override;

    void mdeleteDataByIDOneway(::std::string,
                               StringList,
                               const Ice::Current&) override;

    bool deleteDataByField(::std::string,
                           ::std::string,
                           ::std::string,
                           ErrorInfo&,
                           const Ice::Current&) override;

    bool deleteDataByFields(std::string,
                              std::string,
                              StringList,
                              ErrorInfo&,
                              const Ice::Current&) override;

    bool mdeleteDataByField(std::string,
                              StringList,
                              std::string,
                              ErrorInfo&,
                              const Ice::Current&) override;

    bool mdeleteDataByFields(std::string,
                                       StringList,
                                       StringList,
                                       ErrorInfo&,
                                       const Ice::Current&) override;
};

}

#endif

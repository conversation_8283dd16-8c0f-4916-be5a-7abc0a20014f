#ifndef ZGSPRTSQLSERVER_H
#define ZGSPRTSQLSERVER_H

#include "ZGSPRTDatabase.h"
#ifdef Q_OS_WIN
#include <Windows.h>
#include <sql.h>
#include <sqlext.h>

// 定义一个环境类，用于对SQL Server SQLHENV的全局设置
class ZGSPRTSQLServerEnvironment
{
public:
    ZGSPRTSQLServerEnvironment();
    ~ZGSPRTSQLServerEnvironment();
    SQLHENV getEnv() const;

private:
    SQLHENV m_hEnv;
};

class ZGSPRTSQLServer : public ZGSPRTDatabase
{
public:
    explicit ZGSPRTSQLServer(QObject *parent = nullptr);
    ~ZGSPRTSQLServer() override;

    // ZGSPRTDatabase interface
public:
    bool connect() override;
    void close() override;
    bool execQuery(const QString& sql, const FuncRow& processRow) override;
    bool execCommand(const QString& sql, const FuncResult& processResult) override;
    bool getTables(std::vector<std::string>& tables) override;
    bool getTableFields(const std::string& tableName, std::vector<std::string>& fieldsName) override;
    bool getTableFields(const QString& tableName, QStringList& fieldsName) override;
    bool getTableFields(const std::string& tableName, std::map<std::string, std::string>& fields) override;

private:
    void recordError(SQLSMALLINT handleType, SQLHANDLE handle);

private:
    inline static ZGSPRTSQLServerEnvironment m_env;
    SQLHDBC m_hdbc;
    SQLHSTMT m_hstmt;
};

#endif

#endif // ZGSPRTSQLSERVER_H

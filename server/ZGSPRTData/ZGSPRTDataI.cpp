#include <ZGSPRTDataI.h>
#include "ZGSPRTDataMng.h"
#include "ZGDebugMng.h"

ZG6000::ZGSPRTDataI::ZGSPRTDataI()
{
    ZGSPRTDataMng::instance()->init();
}

bool
ZG6000::ZGSPRTDataI::checkState(const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->checkState();
}

bool
ZG6000::ZGSPRTDataI::getDataByKeyToValue(::std::string key,
                                  ::std::string& value,
                                  ErrorInfo& e,
                                  const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByKeyToValue(key, value, e);
}

bool
ZG6000::ZGSPRTDataI::getDataByKeyToJson(::std::string key,
                                        ::std::string& jsonValue,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByKeyToJson(key, jsonValue, e);
}

bool
ZG6000::ZGSPRTDataI::setDataByKeyFromValue(::std::string key,
                                  ::std::string value,
                                  ErrorInfo& e,
                                  const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->setDataByKeyFromValue(key, value, e);
}

void
ZG6000::ZGSPRTDataI::setDataByKeyFromValueOneway(::std::string key,
                                                 ::std::string value,
                                                 const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->setDataByKeyFromValue(key, value, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByKeyToList(StringList listKey,
                                   StringList& listValue,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByKeyToList(listKey, listValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByKeyToMap(StringList listKey,
                                        StringMap& mapValue,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByKeyToMap(listKey, mapValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByKeyToJson(StringList listKey,
                                         ::std::string& jsonValue,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByKeyToJson(listKey, jsonValue, e);
}

bool
ZG6000::ZGSPRTDataI::msetDataByKeyFromList(StringList listKey,
                                   StringList listValue,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->msetDataByKeyFromList(listKey, listValue, e);
}

void
ZG6000::ZGSPRTDataI::msetDataByKeyFromListOneway(StringList listKey,
                                                 StringList listValue,
                                                 const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->msetDataByKeyFromList(listKey, listValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::msetDataByKeyFromMap(StringMap keyValue,
                                  ErrorInfo& e,
                                  const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->msetDataByKeyFromMap(keyValue, e);
}

void
ZG6000::ZGSPRTDataI::msetDataByKeyFromMapOneway(StringMap keyValue,
                                                const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->msetDataByKeyFromMap(keyValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::getDataByIDToMap(::std::string tableName,
                                 ::std::string id,
                                 StringMap& mapValue,
                                 ErrorInfo& e,
                                 const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByIDToMap(tableName, id, mapValue, e);
}

bool
ZG6000::ZGSPRTDataI::getDataByIDToJson(::std::string tableName,
                                       ::std::string id,
                                       ::std::string& jsonVaule,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByIDToJson(tableName, id, jsonVaule, e);
}

bool
ZG6000::ZGSPRTDataI::getDataByFieldToValue(::std::string tableName,
                                    ::std::string id,
                                    ::std::string fieldName,
                                    ::std::string& fieldValue,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByFieldToValue(tableName, id, fieldName, fieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::getDataByFieldToJson(::std::string tableName,
                                          ::std::string id,
                                          ::std::string fieldName,
                                          ::std::string& jsonFieldValue,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByFieldToJson(tableName, id, fieldName, jsonFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::getDataByFieldsToList(::std::string tableName,
                                     ::std::string id,
                                     StringList listFieldName,
                                     StringList& listFieldValue,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByFieldsToList(tableName, id, listFieldName, listFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::getDataByFieldsToMap(::std::string tableName,
                                        ::std::string id,
                                        StringList listFieldName,
                                        StringMap& mapFieldValue,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByFieldsToMap(tableName, id, listFieldName, mapFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::getDataByFieldsToJson(::std::string tableName,
                                           ::std::string id,
                                           StringList listFieldName,
                                           ::std::string& jsonFieldValue,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->getDataByFieldsToJson(tableName, id, listFieldName, jsonFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByIDToListList(::std::string tableName,
                                   StringList listID,
                                   ListStringList& listFieldValue,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByIDToListList(tableName, listID, listFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByIDToJson(::std::string tableName,
                                         StringList listID,
                                         ::std::string& jsonFieldValue,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByIDToJson(tableName, listID, jsonFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByIDToListMap(::std::string tableName,
                                      StringList listID,
                                      ListStringMap &listFieldMapValue,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByIDToListMap(tableName, listID, listFieldMapValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByIDToMapMap(::std::string tableName,
                                         StringList listID,
                                         MapStringMap& mapFieldValue,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByIDToMapMap(tableName, listID, mapFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByFieldToList(::std::string tableName,
                                     StringList listID,
                                     ::std::string fieldName,
                                     StringList& listFieldValue,
                                     ErrorInfo& e,
                                     const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByFieldToList(tableName, listID, fieldName, listFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByFieldToMap(::std::string tableName,
                                         StringList listID,
                                         ::std::string fieldName,
                                         StringMap& mapValue,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByFieldToMap(tableName, listID, fieldName, mapValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByFieldToJson(::std::string tableName,
                                           StringList listID,
                                           ::std::string fieldName,
                                           ::std::string& jsonFieldValue,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByFieldToJson(tableName, listID, fieldName, jsonFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByFieldsToListList(::std::string tableName,
                                      StringList listID,
                                      StringList listFieldName,
                                      ListStringList& listFieldValue,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByFieldsToListList(tableName, listID, listFieldName, listFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByFieldsToJson(::std::string tableName,
                                            StringList listID,
                                            StringList listFieldName,
                                            ::std::string& jsonFieldValue,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByFieldsToJson(tableName, listID, listFieldName, jsonFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByFieldsToListMap(::std::string tableName,
                                         StringList listID,
                                         StringList listFieldName,
                                         ListStringMap &listFieldMapValue,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByFieldsToListMap(tableName, listID, listFieldName, listFieldMapValue, e);
}

bool
ZG6000::ZGSPRTDataI::mgetDataByFieldsToMapMap(::std::string tableName,
                                            StringList listID,
                                            StringList listFieldName,
                                            MapStringMap& mapFieldValue,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mgetDataByFieldsToMapMap(tableName, listID, listFieldName, mapFieldValue, e);
}

bool
ZG6000::ZGSPRTDataI::updateDataByFieldFromValue(::std::string tableName,
                                       ::std::string id,
                                       ::std::string fieldName,
                                       ::std::string fieldValue,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->updateDataByFieldFromValue(tableName, id, fieldName, fieldValue, e);
}

void
ZG6000::ZGSPRTDataI::updateDataByFieldFromValueOneway(::std::string tableName,
                                                      ::std::string id,
                                                      ::std::string fieldName,
                                                      ::std::string fieldValue,
                                                      const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->updateDataByFieldFromValue(tableName, id, fieldName, fieldValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mupdateDataByFieldFromValue(::std::string tableName,
                                            StringList listID,
                                            ::std::string fieldName,
                                            ::std::string fieldValue,
                                            ErrorInfo& e,
                                            const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mupdateDataByFieldFromValue(tableName, listID, fieldName, fieldValue, e);
}

void
ZG6000::ZGSPRTDataI::mupdateDataByFieldFromValueOneway(::std::string tableName,
                                            StringList listID,
                                            ::std::string fieldName,
                                            ::std::string fieldValue,
                                            const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->mupdateDataByFieldFromValue(tableName, listID, fieldName, fieldValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::updateDataByIDFromMap(::std::string tableName,
                                    ::std::string id,
                                    StringMap mapValue,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->updateDataByIDFromMap(tableName, id, mapValue, e);
}

void
ZG6000::ZGSPRTDataI::updateDataByIDFromMapOneway(::std::string tableName,
                                                 ::std::string id,
                                                 StringMap mapValue,
                                                 const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->updateDataByIDFromMap(tableName, id, mapValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mupdateDataByFieldFromList(::std::string tableName,
                                        StringList listID,
                                        ::std::string fieldName,
                                        StringList listFieldValue,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mupdateDataByFieldFromList(tableName, listID, fieldName, listFieldValue, e);
}

void
ZG6000::ZGSPRTDataI::mupdateDataByFieldFromListOneway(::std::string tableName,
                                        StringList listID,
                                        ::std::string fieldName,
                                        StringList listFieldValue,
                                        const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->mupdateDataByFieldFromList(tableName, listID, fieldName, listFieldValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mupdateValueByFieldsFromListList(::std::string tableName,
                                          StringList listID,
                                          StringList listFieldName,
                                          ListStringList listFieldValue,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mupdateValueByFieldsFromListList(tableName, listID, listFieldName, listFieldValue, e);
}

void
ZG6000::ZGSPRTDataI::mupdateValueByFieldsFromListListOneway(::std::string tableName,
                                                            StringList listID,
                                                            StringList listFieldName,
                                                            ListStringList listFieldValue,
                                                            const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->mupdateValueByFieldsFromListList(tableName, listID, listFieldName, listFieldValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mupdateDataByFieldsFromListMap(::std::string tableName,
                                         StringList listID,
                                         ListStringMap listFieldMapValue,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mupdateDataByFieldsFromListMap(tableName, listID, listFieldMapValue, e);
}

void
ZG6000::ZGSPRTDataI::mupdateDataByFieldsFromListMapOneway(::std::string tableName,
                                         StringList listID,
                                         ListStringMap listFieldMapValue,
                                         const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->mupdateDataByFieldsFromListMap(tableName, listID, listFieldMapValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mupdateDataByFieldsFromMap(::std::string tableName,
                                             StringList listID,
                                             StringMap fieldMapValue,
                                             ErrorInfo& e,
                                             const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mupdateDataByFieldsFromMap(tableName, listID, fieldMapValue, e);
}

void
ZG6000::ZGSPRTDataI::mupdateDataByFieldsFromMapOneway(::std::string tableName,
                                             StringList listID,
                                             StringMap fieldMapValue,
                                             const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->mupdateDataByFieldsFromMap(tableName, listID, fieldMapValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::updateDataID(::std::string tableName,
                                  ::std::string oldID,
                                  ::std::string newID,
                                  ErrorInfo& e,
                                  const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->updateDataID(tableName, oldID, newID, e);
}

void
ZG6000::ZGSPRTDataI::updateDataIDOneway(::std::string tableName,
                                  ::std::string oldID,
                                  ::std::string newID,
                                  const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->updateDataID(tableName, oldID, newID, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mupdateDataID(::std::string tableName,
                                   StringList listOldID,
                                   StringList listNewID,
                                   ErrorInfo& e,
                                   const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mupdateDataID(tableName, listOldID, listNewID, e);
}

void
ZG6000::ZGSPRTDataI::mupdateDataIDOneway(::std::string tableName,
                                   StringList listOldID,
                                   StringList listNewID,
                                   const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->mupdateDataID(tableName, listOldID, listNewID, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::insertDataByFieldFromValue(::std::string tableName,
                                       ::std::string id,
                                       ::std::string fieldName,
                                       ::std::string fieldValue,
                                       ErrorInfo& e,
                                       const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->insertDataByFieldFromValue(tableName, id, fieldName, fieldValue, e);
}

void
ZG6000::ZGSPRTDataI::insertDataByFieldFromValueOneway(::std::string tableName,
                                                      ::std::string id,
                                                      ::std::string fieldName,
                                                      ::std::string fieldValue,
                                                      const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->insertDataByFieldFromValue(tableName, id, fieldName, fieldValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::insertDataByIDFromMap(::std::string tableName,
                                    ::std::string id,
                                    StringMap mapValue,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->insertDataByIDFromMap(tableName, id, mapValue, e);
}

void
ZG6000::ZGSPRTDataI::insertDataByIDFromMapOneway(::std::string tableName,
                                    ::std::string id,
                                    StringMap mapValue,
                                    const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->insertDataByIDFromMap(tableName, id, mapValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::minsertDataByFieldFromList(::std::string tableName,
                                        StringList listID,
                                        ::std::string fieldName,
                                        StringList listFieldValue,
                                        ErrorInfo& e,
                                        const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->minsertDataByFieldFromList(tableName, listID, fieldName, listFieldValue, e);
}

void
ZG6000::ZGSPRTDataI::minsertDataByFieldFromListOneway(::std::string tableName,
                                                      StringList listID,
                                                      ::std::string fieldName,
                                                      StringList listFieldValue,
                                                      const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->minsertDataByFieldFromList(tableName, listID, fieldName, listFieldValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::minsertDataByFieldsFromListMap(::std::string tableName,
                                         StringList listID,
                                         ListStringMap listFieldMapValue,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->minsertDataByFieldsFromListMap(tableName, listID, listFieldMapValue, e);
}

void
ZG6000::ZGSPRTDataI::minsertDataByFieldsFromListMapOneway(::std::string tableName,
                                         StringList listID,
                                         ListStringMap listFieldMapValue,
                                         const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->minsertDataByFieldsFromListMap(tableName, listID, listFieldMapValue, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::deleteDataByID(::std::string tableName,
                                    ::std::string id,
                                    ErrorInfo& e,
                                    const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->deleteDataByID(tableName, id, e);
}

void
ZG6000::ZGSPRTDataI::deleteDataByIDOneway(::std::string tableName,
                                    ::std::string id,
                                    const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->deleteDataByID(tableName, id, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::mdeleteDataByID(::std::string tableName,
                                      StringList listID,
                                      ErrorInfo& e,
                                      const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mdeleteDataByID(tableName, listID, e);
}

void
ZG6000::ZGSPRTDataI::mdeleteDataByIDOneway(::std::string tableName,
                                      StringList listID,
                                      const Ice::Current& current)
{
    ErrorInfo e;
    if (!ZGSPRTDataMng::instance()->mdeleteDataByID(tableName, listID, e))
        ZGLOG_ERROR(e);
}

bool
ZG6000::ZGSPRTDataI::deleteDataByField(::std::string tableName,
                                         ::std::string id,
                                         ::std::string fieldName,
                                         ErrorInfo& e,
                                         const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->deleteDataByField(tableName, id, fieldName, e);
}

bool
ZG6000::ZGSPRTDataI::deleteDataByFields(::std::string tableName,
                                          ::std::string id,
                                          StringList listFieldName,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->deleteDataByFields(tableName, id, listFieldName, e);
}

bool
ZG6000::ZGSPRTDataI::mdeleteDataByField(::std::string tableName,
                                          StringList listID,
                                          ::std::string fieldName,
                                          ErrorInfo& e,
                                          const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mdeleteDataByField(tableName, listID, fieldName, e);
}

bool
ZG6000::ZGSPRTDataI::mdeleteDataByFields(::std::string tableName,
                                           StringList listID,
                                           StringList listFieldName,
                                           ErrorInfo& e,
                                           const Ice::Current& current)
{
    return ZGSPRTDataMng::instance()->mdeleteDataByFields(tableName, listID, listFieldName, e);
}

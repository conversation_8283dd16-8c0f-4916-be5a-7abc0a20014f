#ifndef ZGSPRTMYSQL_H
#define ZGSPRTMYSQL_H

#include "ZGSPRTDatabase.h"
#include "mysql/mysql.h"

class ZGSPRTMySQL : public ZGSPRTDatabase
{
public:
    explicit ZGSPRTMySQL(QObject *parent = nullptr);
    ~ZGSPRTMySQL() override;

    // ZGSPRTDatabase interface
public:
    bool connect() override;
    void close() override;
    bool execQuery(const QString& sql, const FuncRow& processRow) override;
    bool execCommand(const QString& sql, const FuncResult& processResult) override;
    bool getTables(std::vector<std::string>& tables) override;
    bool getTableFields(const std::string& tableName, std::vector<std::string>& fieldsName) override;
    bool getTableFields(const QString& tableName, QStringList& fieldsName) override;
    bool getTableFields(const std::string& tableName, std::map<std::string, std::string>& fields) override;

private:
    MYSQL m_mysql;
};

#endif // ZGSPRTMYSQL_H
